var n=Object.defineProperty;var r=Object.getOwnPropertyDescriptor;var c=Object.getOwnPropertyNames;var i=Object.prototype.hasOwnProperty;var l=(e,t)=>{for(var s in t)n(e,s,{get:t[s],enumerable:!0})},p=(e,t,s,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of c(t))!i.call(e,a)&&a!==s&&n(e,a,{get:()=>t[a],enumerable:!(o=r(t,a))||o.enumerable});return e};var h=e=>p(n({},"__esModule",{value:!0}),e);var u={};l(u,{default:()=>d});module.exports=h(u);var d=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/\b(abstract|assert|boolean|break|byte|case|catch|char|class|continue|const|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|package|private|protected|public|requires|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|transient|try|var|void|volatile|while)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*\()/g},{type:"class",match:/\b[A-Z][\w_]*\b/g}];
