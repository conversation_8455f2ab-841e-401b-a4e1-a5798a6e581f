import { Hono } from 'hono';
// Webhook routes publish messages to Cloudflare Queues; processing happens in index.js queue handler

const app = new Hono();

// Liveness endpoint to validate the mounted route path
app.get('/health', (c) => c.json({ status: 'ok' }));

// Webhook receiver endpoint for ProCore events
app.post('/', async (c) => {
  try {
    // Get the raw body for signature verification
    const rawBody = await c.req.text();
    const signature = c.req.header('X-Procore-Signature') || '';
    const authz = c.req.header('authorization') || '';
    const contentType = c.req.header('content-type') || '';
    // Allow token in query param or custom header for systems that cannot set Authorization header
    let queryToken = '';
    try {
      const url = new URL(c.req.url);
      queryToken = url.searchParams.get('token') || '';
    } catch (_) {}
    const headerToken = c.req.header('X-Webhook-Token') || '';
    
    // Verify using Procore signature when present, otherwise fall back to Authorization Bearer token
    const hmacOk = signature ? (await verifyWebhookSignature(rawBody, signature, c.env.WEBHOOK_SECRET)) : false;
    const bearerOk = await verifyWebhookToken(authz, c.env.WEBHOOK_TOKEN);
    const queryOk = queryToken && c.env.WEBHOOK_TOKEN ? safeEqual(queryToken, c.env.WEBHOOK_TOKEN) : false;
    const headerOk = headerToken && c.env.WEBHOOK_TOKEN ? safeEqual(headerToken, c.env.WEBHOOK_TOKEN) : false;
    const tokenOk = bearerOk || queryOk || headerOk;
    const inSandbox = !c.env.WEBHOOK_SECRET && !c.env.WEBHOOK_TOKEN; // allow when nothing configured
    if (!hmacOk && !tokenOk && !inSandbox) {
      try {
        const hdrs = {};
        for (const [k, v] of c.req.raw.headers.entries()) hdrs[k] = v;
        await c.env.DB.prepare(`
          INSERT INTO webhook_events (
            resource_name, event_type, resource_id, project_id,
            payload, received_at
          ) VALUES (?, ?, ?, ?, ?, ?)
        `)
        .bind(
          'Debug', 'unauthorized', '-', '-',
          JSON.stringify({ url: c.req.url, headers: hdrs, hmacOk, tokenOk, inSandbox }),
          new Date().toISOString()
        ).run();
      } catch (_) {}
      return c.json({ error: 'Unauthorized webhook' }, 401);
    }
    
    // Parse payload supporting JSON and URL-encoded forms
    let payload;
    try {
      payload = parseIncomingPayload(rawBody, contentType);
    } catch (e) {
      try {
        console.error('payload parse error:', e?.message || e);
        await c.env.DB.prepare(`
          INSERT INTO webhook_events (
            resource_name, event_type, resource_id, project_id,
            payload, received_at
          ) VALUES (?, ?, ?, ?, ?, ?)
        `)
        .bind(
          'Debug','invalid_payload','-','-',
          JSON.stringify({ url: c.req.url, content_type: contentType, body_len: (rawBody||'').length }),
          new Date().toISOString()
        ).run();
      } catch (_) {}
      return c.json({ error: 'Invalid payload' }, 400);
    }

    // Debug: log the full payload to understand ProCore's structure
    console.log('[webhook] Full payload received:', JSON.stringify(payload, null, 2));

    // Publish payload to Cloudflare Queue (PROCORE_EVENTS)
    try {
      const peek = { r: payload?.resource_name, e: payload?.event_type, id: payload?.resource_id, p: payload?.project_id };
      console.log('[webhook] received', { ...peek, ct: contentType });
    } catch (_) {}
    try {
      // Send the entire payload to the queue - let the queue consumer handle field extraction
      await c.env.PROCORE_EVENTS.send(payload);
    } catch (e) {
      console.error('enqueue error:', e);
    }
    return c.json({ status: 'queued' }, 200);
  } catch (error) {
    console.error('Error processing webhook:', error);
    return c.json({ error: 'Failed to process webhook' }, 500);
  }
});

// Verify the webhook signature from ProCore
async function verifyWebhookSignature(payload, signature, secret) {
  // If no secret configured, do NOT treat any signature as valid.
  // Sandbox/dev acceptance is handled via `inSandbox` in the route logic.
  if (!secret) return false;
  try {
    const enc = new TextEncoder();
    const keyData = enc.encode(secret);
    const key = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: { name: 'SHA-256' } },
      false,
      ['sign']
    );
    const sig = await crypto.subtle.sign('HMAC', key, enc.encode(payload));
    // Compute hex and base64 renditions for flexible compare
    const bytes = new Uint8Array(sig);
    const hex = [...bytes].map(b => b.toString(16).padStart(2, '0')).join('');
    const b64 = btoa(String.fromCharCode(...bytes));
    const provided = (signature || '').trim().toLowerCase();
    // Accept formats: hex, sha256=hex, base64, sha256=base64
    const normalized = provided.startsWith('sha256=') ? provided.slice(7) : provided;
    const ok = safeEqual(normalized, hex) || safeEqual(normalized, b64.toLowerCase());
    return ok;
  } catch (e) {
    console.error('Signature verification error:', e);
    return false;
  }
}

// Diagnostic: open capture endpoint to verify deliveries reach the Worker.
// IMPORTANT: Place this BEFORE the pathToken route so '/capture' does not match '/:pathToken'
app.post('/capture', async (c) => {
  try {
    const rawBody = await c.req.text();
    // Build a headers map
    const hdrs = {};
    try {
      for (const [k, v] of c.req.raw.headers.entries()) {
        hdrs[k] = v;
      }
    } catch (_) {}
    const payload = {
      method: c.req.method,
      url: c.req.url,
      headers: hdrs,
      body: rawBody
    };
    try {
      await c.env.DB.prepare(`
        INSERT INTO webhook_events (
          resource_name, event_type, resource_id, project_id,
          payload, received_at
        ) VALUES (?, ?, ?, ?, ?, ?)
      `)
      .bind('Debug', 'capture', '-', '-', JSON.stringify(payload), new Date().toISOString())
      .run();
    } catch (e) {
      console.error('capture insert error:', e);
    }
    return c.json({ status: 'ok', note: 'Captured. Check /api/admin/webhooks/logs' });
  } catch (error) {
    console.error('Error in capture endpoint:', error);
    return c.json({ error: 'Failed to capture' }, 500);
  }
});

function safeEqual(a, b) {
  if (a.length !== b.length) return false;
  let diff = 0;
  for (let i = 0; i < a.length; i++) diff |= a.charCodeAt(i) ^ b.charCodeAt(i);
  return diff === 0;
}

async function verifyWebhookToken(authorizationHeader, expectedToken) {
  // If no token configured, don't require it
  if (!expectedToken) return false;
  const prefix = 'bearer ';
  const v = (authorizationHeader || '').trim();
  if (!v.toLowerCase().startsWith(prefix)) return false;
  const provided = v.slice(prefix.length).trim();
  return safeEqual(provided, expectedToken);
}

// Best-effort payload parser supporting JSON and URL-encoded webhook forms
function parseIncomingPayload(rawBody, contentType) {
  const body = rawBody || '';
  const ct = (contentType || '').toLowerCase();
  // JSON content types
  if (ct.includes('application/json') || ct.includes('+json') || body.trim().startsWith('{') || body.trim().startsWith('[')) {
    return JSON.parse(body || '{}');
  }
  // URL-encoded forms (payload=<json> or discrete fields)
  if (ct.includes('application/x-www-form-urlencoded')) {
    const params = new URLSearchParams(body);
    // Common pattern: payload=<json>
    const payloadStr = params.get('payload') || params.get('data');
    if (payloadStr) {
      const val = decodeURIComponent(payloadStr);
      return JSON.parse(val);
    }
    // Otherwise, construct an object from known keys
    const o = {};
    for (const [k, v] of params.entries()) o[k] = v;
    return o;
  }
  // Text/plain that actually contains JSON
  if (ct.includes('text/plain')) {
    return JSON.parse(body || '{}');
  }
  // Fallback: attempt JSON, else throw
  try { return JSON.parse(body || '{}'); } catch (e) { throw e; }
}

export const webhookRoutes = app;

// Support token in path as a last-resort option: POST /api/webhooks/:token
app.post('/:pathToken', async (c) => {
  try {
    const rawBody = await c.req.text();
    const signature = c.req.header('X-Procore-Signature') || '';
    const authz = c.req.header('authorization') || '';
    const headerToken = c.req.header('X-Webhook-Token') || '';
    const pathToken = c.req.param('pathToken') || '';
    const contentType = c.req.header('content-type') || '';

    const hmacOk = signature ? (await verifyWebhookSignature(rawBody, signature, c.env.WEBHOOK_SECRET)) : false;
    const bearerOk = await verifyWebhookToken(authz, c.env.WEBHOOK_TOKEN);
    const headerOk = headerToken && c.env.WEBHOOK_TOKEN ? safeEqual(headerToken, c.env.WEBHOOK_TOKEN) : false;
    const pathOk = pathToken && c.env.WEBHOOK_TOKEN ? safeEqual(pathToken, c.env.WEBHOOK_TOKEN) : false;
    const tokenOk = bearerOk || headerOk || pathOk;
    const inSandbox = !c.env.WEBHOOK_SECRET && !c.env.WEBHOOK_TOKEN;
    if (!hmacOk && !tokenOk && !inSandbox) {
      // Unauthorized: record and reject
      try {
        const hdrs = {};
        for (const [k, v] of c.req.raw.headers.entries()) hdrs[k] = v;
        await c.env.DB.prepare(`
          INSERT INTO webhook_events (
            resource_name, event_type, resource_id, project_id,
            payload, received_at
          ) VALUES (?, ?, ?, ?, ?, ?)
        `).bind('Debug','unauthorized','-','-', JSON.stringify({ url: c.req.url, headers: hdrs, pathToken }), new Date().toISOString()).run();
      } catch (_) {}
      return c.json({ error: 'Unauthorized webhook' }, 401);
    }

    // Authorized: parse payload and publish to queue
    let payload;
    try {
      payload = parseIncomingPayload(rawBody, contentType);
    } catch (e) {
      try {
        console.error('payload parse error (path):', e?.message || e);
        await c.env.DB.prepare(`
          INSERT INTO webhook_events (
            resource_name, event_type, resource_id, project_id,
            payload, received_at
          ) VALUES (?, ?, ?, ?, ?, ?)
        `)
        .bind(
          'Debug','invalid_payload','-','-',
          JSON.stringify({ url: c.req.url, content_type: contentType, body_len: (rawBody||'').length }),
          new Date().toISOString()
        ).run();
      } catch (_) {}
      return c.json({ error: 'Invalid payload' }, 400);
    }
    console.log('[webhook:path] Full payload received:', JSON.stringify(payload, null, 2));
    try {
      const peek = { r: payload?.resource_name, e: payload?.event_type, id: payload?.resource_id, p: payload?.project_id };
      console.log('[webhook:path] received', { ...peek, ct: contentType });
    } catch (_) {}
    try {
      // Send the entire payload to the queue - let the queue consumer handle field extraction
      await c.env.PROCORE_EVENTS.send(payload);
      try { console.log('[webhook:path] queued'); } catch (_) {}
    } catch (e) {
      console.error('enqueue error:', e);
    }
    return c.json({ status: 'queued' }, 200);
  } catch (error) {
    console.error('Error processing webhook (path token):', error);
    return c.json({ error: 'Failed to process webhook' }, 500);
  }
});

// Trailing-slash alias: POST /api/webhooks/:token/
app.post('/:pathToken/', async (c) => {
  // Delegate to the non-slash handler by rewriting the pathToken
  // Re-run the same logic as above to keep behavior identical
  try {
    const rawBody = await c.req.text();
    const signature = c.req.header('X-Procore-Signature') || '';
    const authz = c.req.header('authorization') || '';
    const headerToken = c.req.header('X-Webhook-Token') || '';
    const pathToken = (c.req.param('pathToken') || '').replace(/\/$/, '');
    const contentType = c.req.header('content-type') || '';

    const hmacOk = signature ? (await verifyWebhookSignature(rawBody, signature, c.env.WEBHOOK_SECRET)) : false;
    const bearerOk = await verifyWebhookToken(authz, c.env.WEBHOOK_TOKEN);
    const headerOk = headerToken && c.env.WEBHOOK_TOKEN ? safeEqual(headerToken, c.env.WEBHOOK_TOKEN) : false;
    const pathOk = pathToken && c.env.WEBHOOK_TOKEN ? safeEqual(pathToken, c.env.WEBHOOK_TOKEN) : false;
    const tokenOk = bearerOk || headerOk || pathOk;
    const inSandbox = !c.env.WEBHOOK_SECRET && !c.env.WEBHOOK_TOKEN;
    if (!hmacOk && !tokenOk && !inSandbox) {
      try {
        const hdrs = {};
        for (const [k, v] of c.req.raw.headers.entries()) hdrs[k] = v;
        await c.env.DB.prepare(`
          INSERT INTO webhook_events (
            resource_name, event_type, resource_id, project_id,
            payload, received_at
          ) VALUES (?, ?, ?, ?, ?, ?)
        `).bind('Debug','unauthorized','-','-', JSON.stringify({ url: c.req.url, headers: hdrs, pathToken }), new Date().toISOString()).run();
      } catch (_) {}
      return c.json({ error: 'Unauthorized webhook' }, 401);
    }

    let payload;
    try {
      payload = parseIncomingPayload(rawBody, contentType);
    } catch (e) {
      try {
        console.error('payload parse error (path/ts):', e?.message || e);
        await c.env.DB.prepare(`
          INSERT INTO webhook_events (
            resource_name, event_type, resource_id, project_id,
            payload, received_at
          ) VALUES (?, ?, ?, ?, ?, ?)
        `)
        .bind(
          'Debug','invalid_payload','-','-',
          JSON.stringify({ url: c.req.url, content_type: contentType, body_len: (rawBody||'').length }),
          new Date().toISOString()
        ).run();
      } catch (_) {}
      return c.json({ error: 'Invalid payload' }, 400);
    }
    console.log('[webhook:path/] Full payload received:', JSON.stringify(payload, null, 2));
    try {
      const peek = { r: payload?.resource_name, e: payload?.event_type, id: payload?.resource_id, p: payload?.project_id };
      console.log('[webhook:path/] received', { ...peek, ct: contentType });
    } catch (_) {}
    try {
      // Send the entire payload to the queue - let the queue consumer handle field extraction
      await c.env.PROCORE_EVENTS.send(payload);
      try { console.log('[webhook:path/] queued'); } catch (_) {}
    } catch (e) {
      console.error('enqueue error:', e);
    }
    return c.json({ status: 'queued' }, 200);
  } catch (error) {
    console.error('Error processing webhook (path token slash):', error);
    return c.json({ error: 'Failed to process webhook' }, 500);
  }
});
