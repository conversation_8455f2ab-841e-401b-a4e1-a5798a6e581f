var p=Object.defineProperty;var h=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var o=Object.prototype.hasOwnProperty;var d=(e,t)=>{for(var c in t)p(e,c,{get:t[c],enumerable:!0})},u=(e,t,c,g)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of m(t))!o.call(e,a)&&a!==c&&p(e,a,{get:()=>t[a],enumerable:!(g=h(t,a))||g.enumerable});return e};var y=e=>u(p({},"__esModule",{value:!0}),e);var r={};d(r,{default:()=>f});module.exports=y(r);var f=[{match:/[^\[\->+.<\]\s].*/g,sub:"todo"},{type:"func",match:/\.+/g},{type:"kwd",match:/[<>]+/g},{type:"oper",match:/[+-]+/g}];
