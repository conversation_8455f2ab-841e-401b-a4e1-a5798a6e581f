    import { Hono } from 'hono';
import { projectRoutes } from './routes/projects';
import { webhookRoutes } from './routes/webhooks';
import { adminRoutes } from './routes/admin';
import { authRoutes } from './routes/auth';
import { syncRoutes, debugRoutes } from './routes/sync';
import { testRoutes } from './routes/test';
import { authMiddleware } from './middleware/auth';
import { initializeDatabase, checkDatabaseHealth } from './db/init';

const app = new Hono();

// Middleware
// Ensure DB migrations run once per cold start before serving requests
let __migrationsPromise;
app.use('*', async (c, next) => {
  if (!__migrationsPromise) {
    __migrationsPromise = (async () => {
      try { await initializeDatabase(c.env); } catch (e) { console.error('migrations failed:', e); }
    })();
  }
  await __migrationsPromise;
  await next();
});

// Simple logging middleware
app.use('*', async (c, next) => {
  console.log(`${c.req.method} ${c.req.url}`);
  await next();
});
// Simple CORS middleware
app.use('*', async (c, next) => {
  c.res.headers.set('Access-Control-Allow-Origin', '*');
  c.res.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  c.res.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (c.req.method === 'OPTIONS') {
    return new Response(null, { status: 204 });
  }
  
  await next();
});

// Health check endpoint
app.get('/health', async (c) => {
  try {
    const dbHealth = await checkDatabaseHealth(c.env);
    return c.json({
      status: 'ok',
      version: '0.1.0',
      database: dbHealth
    });
  } catch (error) {
    return c.json({
      status: 'error',
      message: error.message
    }, 500);
  }
});

// API Routes
app.route('/api/projects', projectRoutes);
app.route('/api/webhooks', webhookRoutes);
app.route('/api/sync', syncRoutes);
app.route('/api/test', testRoutes);
app.route('/auth', authRoutes);

// Debug routes (no auth required)
app.route('/debug', debugRoutes);

// Admin Routes (protected)
app.use('/api/admin/*', authMiddleware);
app.route('/api/admin', adminRoutes);

// Static assets for the customer portal frontend
app.get('/', async (c) => {
  return c.html(`<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ProCore Customer Portal</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    header {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e0e0e0;
    }
    h1 {
      color: #0066cc;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .card-title {
      color: #0066cc;
      margin-top: 0;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      font-size: 0.9rem;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>ProCore Customer Portal</h1>
      <p>View your kitchen project status, milestones, updates, photos, and documents</p>
    </header>
    
    <div class="card">
      <h2 class="card-title">Welcome</h2>
      <p>This is a lightweight version of the ProCore Customer Portal. The full version will include:</p>
      <ul>
        <li>Project overview and status</li>
        <li>Milestone tracking</li>
        <li>Project updates</li>
        <li>Photo gallery</li>
        <li>Document access</li>
      </ul>
      <p>To access your project, you'll need a magic link or access token.</p>
    </div>
    
    <div class="card">
      <h2 class="card-title">API Status</h2>
      <p>The API is running and ready to serve data from ProCore.</p>
      <p>Check the API status at <a href="/health">/health</a></p>
    </div>
    
    <div class="card">
      <h2 class="card-title">ProCore Data Sync</h2>
      <p>Use the button below to sync data from ProCore:</p>
      <button id="syncButton" style="background-color: #0066cc; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Sync ProCore Data</button>
      <p id="syncStatus"></p>
      
      <script>
        document.getElementById('syncButton').addEventListener('click', () => {
          const statusElement = document.getElementById('syncStatus');
          statusElement.textContent = 'Syncing data from ProCore...';
          
          // Use a regular promise chain instead of async/await to avoid the asynchronous response error
          fetch('/api/sync/run', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer dev-admin-token'
            }
          })
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.json();
          })
          .then(result => {
            const projects = result.results && result.results.projects ? result.results.projects : 0;
            const photos = result.results && result.results.photos ? result.results.photos : 0;
            const documents = result.results && result.results.documents ? result.results.documents : 0;
            statusElement.textContent = 'Sync completed successfully! Projects: ' + projects + ', Photos: ' + photos + ', Documents: ' + documents;
          })
          .catch(error => {
            statusElement.textContent = 'Error syncing data. Please check the console for details.';
            console.error('Sync error:', error);
          });
        });
      </script>
    </div>
    
    <div class="card">
      <h2 class="card-title">Project Refresh</h2>
      <p>Use the buttons below to refresh individual project data:</p>
      <div id="projectList" style="margin-top: 15px;">
        <div class="project-card" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 4px;">
          <h3 style="margin-top: 0;">Project 279664</h3>
          <button class="refresh-button" data-project-id="279664" style="background-color: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Refresh Project Data</button>
          <span class="refresh-status" id="refresh-status-279664" style="margin-left: 10px;"></span>
        </div>
        
        <div class="project-card" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 4px;">
          <h3 style="margin-top: 0;">Project 279363</h3>
          <button class="refresh-button" data-project-id="279363" style="background-color: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Refresh Project Data</button>
          <span class="refresh-status" id="refresh-status-279363" style="margin-left: 10px;"></span>
        </div>
        
        <div class="project-card" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 4px;">
          <h3 style="margin-top: 0;">Add Custom Project</h3>
          <div style="display: flex; margin-bottom: 10px;">
            <input type="text" id="custom-project-id" placeholder="Enter Project ID" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;">
            <button id="add-project-button" style="background-color: #0066cc; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Add Project</button>
          </div>
          <div id="custom-project-container"></div>
        </div>
      </div>
      
      <script>
        // Add event listeners to refresh buttons
        document.querySelectorAll('.refresh-button').forEach(button => {
          button.addEventListener('click', handleRefreshClick);
        });
        
        // Handle refresh button click
        function handleRefreshClick(event) {
          const button = event.target;
          const projectId = button.getAttribute('data-project-id');
          const statusElement = document.getElementById('refresh-status-' + projectId);
          
          // Update UI
          button.disabled = true;
          statusElement.textContent = 'Refreshing...';
          
          // Call the refresh endpoint
          fetch('/api/projects/' + projectId + '/refresh', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer dev-admin-token'
            }
          })
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.json();
          })
          .then(result => {
            statusElement.textContent = 'Project data refreshed successfully!';
            statusElement.style.color = '#4CAF50';
            console.log('Refresh result:', result);
          })
          .catch(error => {
            statusElement.textContent = 'Error: ' + error.message;
            statusElement.style.color = '#f44336';
            console.error('Refresh error:', error);
          })
          .finally(() => {
            button.disabled = false;
            
            // Clear status message after 5 seconds
            setTimeout(() => {
              statusElement.textContent = '';
            }, 5000);
          });
        }
        
        // Add custom project
        document.getElementById('add-project-button').addEventListener('click', () => {
          const projectIdInput = document.getElementById('custom-project-id');
          const projectId = projectIdInput.value.trim();
          
          if (!projectId) {
            alert('Please enter a project ID');
            return;
          }
          
          // Check if project already exists
          if (document.getElementById('refresh-status-' + projectId)) {
            alert('Project already exists');
            return;
          }
          
          // Create new project card
          const container = document.getElementById('custom-project-container');
          const projectCard = document.createElement('div');
          projectCard.className = 'project-card';
          projectCard.style.border = '1px solid #ddd';
          projectCard.style.padding = '15px';
          projectCard.style.marginBottom = '10px';
          projectCard.style.borderRadius = '4px';
          projectCard.style.marginTop = '10px';
          
          projectCard.innerHTML = 
            '<h3 style="margin-top: 0;">Project ' + projectId + '</h3>' +
            '<button class="refresh-button" data-project-id="' + projectId + '" style="background-color: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Refresh Project Data</button>' +
            '<span class="refresh-status" id="refresh-status-' + projectId + '" style="margin-left: 10px;"></span>';
          
          container.appendChild(projectCard);
          
          // Add event listener to the new button
          projectCard.querySelector('.refresh-button').addEventListener('click', handleRefreshClick);
          
          // Clear input
          projectIdInput.value = '';
        });
      </script>
    </div>
  </div>
  
  <footer>
    &copy; 2025 One-Off Kitchen Customer Portal
  </footer>
</body>
</html>`);
});

// API endpoints are already configured above

// Serve the refresh.html file
app.get('/refresh', async (c) => {
  const refreshHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Project Refresh Tool</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    header {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e0e0e0;
    }
    h1 {
      color: #0066cc;
    }
    .project-card {
      background-color: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }
    .project-title {
      color: #0066cc;
      margin-top: 0;
      font-size: 1.5rem;
    }
    .refresh-button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-right: 10px;
    }
    .refresh-button:hover {
      background-color: #45a049;
    }
    .refresh-button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .refresh-status {
      font-size: 14px;
      margin-left: 10px;
    }
    .refresh-status.success {
      color: #4CAF50;
    }
    .refresh-status.error {
      color: #f44336;
    }
    .add-project-form {
      margin-top: 20px;
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 8px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .submit-button {
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
    }
    .submit-button:hover {
      background-color: #0055aa;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>Project Refresh Tool</h1>
      <p>Use this page to refresh project data from ProCore</p>
    </header>
    
    <div class="project-card">
      <h2 class="project-title">Controls</h2>
      <button class="submit-button" id="run-cron">Run Full Sync Now</button>
      <button class="submit-button" id="reload-projects" style="margin-left: 10px;">Refresh Project List</button>
      <span class="refresh-status" id="cron-status" style="margin-left: 10px;"></span>
    </div>
    
    <div class="project-list">
      <div class="project-card">
        <h2 class="project-title">Project 279664</h2>
        <button class="refresh-button" id="refresh-279664">Refresh Project Data</button>
        <span class="refresh-status" id="status-279664"></span>
      </div>
      
      <div class="project-card">
        <h2 class="project-title">Project 279363</h2>
        <button class="refresh-button" id="refresh-279363">Refresh Project Data</button>
        <span class="refresh-status" id="status-279363"></span>
      </div>
    </div>
    
    <div class="add-project-form">
      <h2>Add Custom Project</h2>
      <div class="form-group">
        <label for="projectId">Project ID:</label>
        <input type="text" id="projectId" placeholder="Enter ProCore project ID">
      </div>
      <button class="submit-button" id="add-project-button">Add Project</button>
    </div>
    
    <div id="custom-projects"></div>
    
    <div class="project-card" style="margin-top: 20px;">
      <h2 class="project-title">Discovered Projects</h2>
      <div id="discovered-projects"></div>
    </div>
  </div>

  <script>
    // Configuration
    const API_BASE_URL = window.location.origin;
    const AUTH_TOKEN = 'dev-admin-token';
    
    // Refresh project data
    function refreshProject(projectId) {
      const button = document.getElementById('refresh-' + projectId);
      const statusElement = document.getElementById('status-' + projectId);
      
      // Update UI
      button.disabled = true;
      statusElement.textContent = 'Refreshing...';
      statusElement.className = 'refresh-status';
      
      // Call the refresh endpoint
      fetch(API_BASE_URL + '/api/projects/' + projectId + '/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + AUTH_TOKEN
        }
      })
      .then(function(response) {
        if (!response.ok) {
          throw new Error('HTTP error ' + response.status);
        }
        return response.json();
      })
      .then(function(data) {
        statusElement.textContent = 'Project data refreshed successfully!';
        statusElement.className = 'refresh-status success';
        console.log('Refresh result:', data);
      })
      .catch(function(error) {
        statusElement.textContent = 'Error: ' + error.message;
        statusElement.className = 'refresh-status error';
        console.error('Refresh error:', error);
      })
      .finally(function() {
        button.disabled = false;
        
        // Clear status after 5 seconds
        setTimeout(function() {
          statusElement.textContent = '';
          statusElement.className = 'refresh-status';
        }, 5000);
      });
    }
    
    // Add a new project
    function addProject() {
      const projectId = document.getElementById('projectId').value.trim();
      
      if (!projectId) {
        alert('Please enter a project ID');
        return;
      }
      
      // Check if project already exists
      if (document.getElementById('status-' + projectId)) {
        alert('Project already exists');
        return;
      }
      
      // Create new project card
      const customProjects = document.getElementById('custom-projects');
      const projectCard = document.createElement('div');
      projectCard.className = 'project-card';
      
      // Create elements
      const title = document.createElement('h2');
      title.className = 'project-title';
      title.textContent = 'Project ' + projectId;
      
      const button = document.createElement('button');
      button.className = 'refresh-button';
      button.id = 'refresh-' + projectId;
      button.textContent = 'Refresh Project Data';
      button.addEventListener('click', function() {
        refreshProject(projectId);
      });
      
      const status = document.createElement('span');
      status.className = 'refresh-status';
      status.id = 'status-' + projectId;
      
      // Add elements to card
      projectCard.appendChild(title);
      projectCard.appendChild(button);
      projectCard.appendChild(status);
      
      // Add card to container
      customProjects.appendChild(projectCard);
      
      // Clear input
      document.getElementById('projectId').value = '';
    }
    
    // Load discovered projects (fallback version)
    function fetchProjectsList() {
      fetch(API_BASE_URL + '/api/projects')
      .then(function(response) {
        if (!response.ok) {
          throw new Error('HTTP error ' + response.status);
        }
        return response.json();
      })
      .then(function(data) {
        const discoveredProjects = document.getElementById('discovered-projects');
        // Append new cards only; keep any existing
        data.forEach(function(project) {
          const pid = String(project.external_id || project.id);
          if (document.getElementById('status-' + pid)) return;
          const projectCard = document.createElement('div');
          projectCard.className = 'project-card';
          
          // Create elements
          const title = document.createElement('h2');
          title.className = 'project-title';
          title.textContent = (project.name ? project.name + ' ' : '') + '(Project ' + pid + ')';
          
          const button = document.createElement('button');
          button.className = 'refresh-button';
          button.id = 'refresh-' + pid;
          button.textContent = 'Refresh Project Data';
          button.addEventListener('click', function() {
            refreshProject(pid);
          });
          
          const status = document.createElement('span');
          status.className = 'refresh-status';
          status.id = 'status-' + pid;
          
          // Add elements to card
          projectCard.appendChild(title);
          projectCard.appendChild(button);
          projectCard.appendChild(status);
          
          // Add card to container
          discoveredProjects.appendChild(projectCard);
        });
      })
      .catch(function(error) {
        console.error('Error loading discovered projects:', error);
      });
    }
    
    // Run full sync now
    function runCronNow() {
      const cronStatus = document.getElementById('cron-status');
      cronStatus.textContent = 'Starting full sync...';
      cronStatus.className = 'refresh-status';
      fetch(API_BASE_URL + '/api/sync/run', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + AUTH_TOKEN
        }
      })
      .then(function(response) {
        if (!response.ok) {
          throw new Error('HTTP error ' + response.status);
        }
        return response.json();
      })
      .then(function(data) {
        cronStatus.textContent = 'Full sync started; updating project list...';
        cronStatus.className = 'refresh-status success';
        setTimeout(fetchProjectsList, 1500);
      })
      .catch(function(error) {
        cronStatus.textContent = 'Error: ' + error.message;
        console.error('Full sync error:', error);
      })
      .finally(function() {
        setTimeout(function() { cronStatus.textContent = ''; cronStatus.className = 'refresh-status'; }, 5000);
      });
    }
    
    // Add event listeners when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      // Add event listeners to refresh buttons
      document.getElementById('refresh-279664').addEventListener('click', function() {
        refreshProject('279664');
      });
      
      document.getElementById('refresh-279363').addEventListener('click', function() {
        refreshProject('279363');
      });
      
      // Add event listener to add project button
      document.getElementById('add-project-button').addEventListener('click', addProject);
      
      // Load discovered projects on page load
      fetchProjectsList();
      
      // Wire controls
      document.getElementById('reload-projects').addEventListener('click', fetchProjectsList);
      document.getElementById('run-cron').addEventListener('click', runCronNow);
    });
  </script>
</body>
</html>`
  return c.html(refreshHtml);
});


// Error handling
app.onError((err, c) => {
  console.error(`Error: ${err.message}`);
  return c.json({ error: 'An error occurred processing your request' }, 500);
});

export default {
  fetch: app.fetch,
  
  // CRON handler: run a FULL SYNC every 60 minutes
  async scheduled(event, env, ctx) {
    const startedAt = new Date().toISOString();
    console.log(`[CRON] Triggered (${event.cron}) at ${startedAt}`);
    try {
      // Ensure DB is initialized before syncing
      await initializeDatabase(env);

      // Dynamically import the full sync to avoid circular deps at module load
      const { syncProCoreData } = await import('./services/procore-api');

      // Kick off the full sync and wait for completion within the cron invocation
      const result = await syncProCoreData(env);

      const projects = result?.results?.projects ?? 0;
      const photos = result?.results?.photos ?? 0;
      const documents = result?.results?.documents ?? 0;
      console.log(`[CRON] Full sync completed. Projects: ${projects}, Photos: ${photos}, Documents: ${documents}`);

      // After D1 sync completes, mirror assets to R2 (best effort, in background)
      try {
        const { mirrorPhotoToR2, mirrorDocumentToR2 } = await import('./services/r2-sync');

        // Fetch active projects
        const projRows = await env.DB.prepare('SELECT external_id FROM projects WHERE status = "active"').all();
        const projList = (projRows?.results || []).map(r => String(r.external_id));
        console.log(`[CRON] Mirroring to R2 for ${projList.length} active projects`);

        // Concurrency helper
        async function mirrorSetWithLimit(items, limit, fn) {
          const queue = items.slice();
          const workers = Array.from({ length: Math.min(limit, queue.length) }).map(async () => {
            while (queue.length) {
              const next = queue.shift();
              try { await fn(next); } catch (e) { console.error('[CRON] mirror error', e?.message || e); }
            }
          });
          await Promise.all(workers);
        }

        for (const pid of projList) {
          try {
            // Collect doc and photo IDs from D1
            const docRows = await env.DB.prepare('SELECT external_id FROM documents WHERE project_id = ?').bind(pid).all();
            const photoRows = await env.DB.prepare('SELECT external_id FROM photos WHERE project_id = ?').bind(pid).all();
            const docIds = (docRows?.results || []).map(r => String(r.external_id));
            const photoIds = (photoRows?.results || []).map(r => String(r.external_id));
            console.log(`[CRON] Project ${pid}: mirror ${docIds.length} docs, ${photoIds.length} photos`);

            // Dispatch mirroring in background with modest concurrency per project
            const mirrorJob = (async () => {
              await mirrorSetWithLimit(docIds, 4, async (id) => { await mirrorDocumentToR2(pid, id, env); });
              await mirrorSetWithLimit(photoIds, 4, async (id) => { await mirrorPhotoToR2(pid, id, env); });
            })();

            try { ctx.waitUntil(mirrorJob); } catch (_) { await mirrorJob; }
          } catch (perr) {
            console.error(`[CRON] Mirror planning failed for project ${pid}:`, perr?.message || perr);
          }
        }
      } catch (mirrorErr) {
        console.error('[CRON] Mirroring stage failed to start:', mirrorErr?.message || mirrorErr);
      }

      // Optionally record a sync heartbeat (does not fail the job if it errors)
      try {
        await env.DB.prepare(
          'INSERT INTO sync_heartbeats (ran_at, projects, photos, documents) VALUES (?, ?, ?, ?)'
        ).bind(new Date().toISOString(), projects, photos, documents).run();
      } catch (_) {
        // Table may not exist; that's fine. You can add it to schema if desired.
      }
    } catch (error) {
      console.error(`[CRON] Full sync failed: ${error?.message || error}`);
    }
  },
  // Cloudflare Queues consumer: process Procore webhook messages
  async queue(batch, env, ctx) {
    for (const msg of batch.messages) {
      try {
        let payload = msg.body;
        if (typeof payload === 'string') {
          try { payload = JSON.parse(payload); } catch (_) { payload = {}; }
        }
        
        // Extract key information for logging and retry decisions
        const resourceName = payload?.resource_name || 'Unknown';
        const eventType = payload?.event_type || 'Unknown';
        const resourceId = String(payload?.resource_id || '-');
        const projectId = String(payload?.project_id || '-');
        
        // Debug: persist a receipt that the queue consumer picked up the message
        try {
          await env.DB.prepare(`
            INSERT INTO webhook_events (
              resource_name, event_type, resource_id, project_id,
              payload, received_at
            ) VALUES (?, ?, ?, ?, ?, ?)
          `).bind(
            resourceName, 'queue_received', resourceId, projectId,
            JSON.stringify({ payload }), new Date().toISOString()
          ).run();
        } catch (_) {}
        
        // Add a delay before processing newly created items to prevent 404s
        // as recommended in the ProCore Webhook Overview document
        if (eventType.toLowerCase().includes('create')) {
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
        }
        
        // Process the webhook
        const { processWebhook } = await import('./services/webhookService');
        await processWebhook(payload, env);
        
        try { 
          console.log('[queues] processed', { 
            r: resourceName, 
            e: eventType, 
            id: resourceId, 
            p: projectId 
          }); 
        } catch (_) {}
        
        // Acknowledge successful processing
        msg.ack();
      } catch (e) {
        console.error('[queues] error', e?.message || e);
        
        // Extract error information for retry decisions
        const errorMessage = e?.message || String(e);
        const errorCode = e?.status || (errorMessage.includes('429') ? 429 : 0);
        const retryAfter = e?.headers?.get('X-Rate-Limit-Reset') || null;
        
        // Debug: persist queue error for visibility in admin logs
        try {
          await env.DB.prepare(`
            INSERT INTO webhook_events (
              resource_name, event_type, resource_id, project_id,
              payload, received_at
            ) VALUES (?, ?, ?, ?, ?, ?)
          `).bind(
            'Debug', 'queue_error', '-', '-',
            JSON.stringify({ 
              error: errorMessage,
              errorCode,
              retryAfter,
              retryCount: msg.retryCount || 0
            }), 
            new Date().toISOString()
          ).run();
        } catch (_) {}
        
        // Implement retry strategy based on error type
        try { 
          // For rate limiting (429), honor the X-Rate-Limit-Reset header
          if (errorCode === 429 && retryAfter) {
            const retryDelayMs = parseInt(retryAfter, 10) * 1000;
            msg.retry({ delaySeconds: Math.ceil(retryDelayMs / 1000) });
          }
          // For 404 errors (resource not found), use a progressive backoff
          else if (errorCode === 404) {
            const retryCount = msg.retryCount || 0;
            if (retryCount < 3) { // Max 3 retries for 404s
              const delaySeconds = [2, 10, 30][retryCount]; // Progressive backoff
              msg.retry({ delaySeconds });
            } else {
              // After 3 retries, log and drop the message
              console.error('[queues] dropping message after max retries for 404:', 
                msg.body?.resource_name, msg.body?.resource_id);
            }
          }
          // For server errors (5xx) or network issues, use exponential backoff
          else if (errorCode >= 500 || errorCode === 0) {
            const retryCount = msg.retryCount || 0;
            if (retryCount < 5) { // Max 5 retries
              const delaySeconds = Math.min(Math.pow(2, retryCount), 60); // Exponential backoff, max 60s
              msg.retry({ delaySeconds });
            } else {
              // After max retries, log and drop the message
              console.error('[queues] dropping message after max retries for server error:', 
                msg.body?.resource_name, msg.body?.resource_id);
            }
          }
          // For other errors, use default retry
          else {
            msg.retry();
          }
        } catch (_) {}
      }
    }
  },
};

