var c=Object.defineProperty;var F=Object.getOwnPropertyDescriptor;var y=Object.getOwnPropertyNames;var x=Object.prototype.hasOwnProperty;var $=(s,t)=>{for(var m in t)c(s,m,{get:t[m],enumerable:!0})},l=(s,t,m,g)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of y(t))!x.call(s,a)&&a!==m&&c(s,a,{get:()=>t[a],enumerable:!(g=F(t,a))||g.enumerable});return s};var E=s=>l(c({},"__esModule",{value:!0}),s);var i={};$(i,{default:()=>o});module.exports=E(i);var r=":A-Z_a-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",b=r+"\\-\\.0-9\xB7\u0300-\u036F\u203F-\u2040",p=`[${r}][${b}]*`,u=`\\s*(\\s+${p}\\s*(=\\s*([^"']\\S*|("|')(\\\\[^]|(?!\\4)[^])*\\4?)?)?\\s*)*`,e={match:RegExp(`<[/!?]?${p}${u}[/!?]?>`,"g"),sub:[{type:"var",match:RegExp(`^<[/!?]?${p}`,"g"),sub:[{type:"oper",match:/^<[\/!?]?/g}]},{type:"str",match:/=\s*([^"']\S*|("|')(\\[^]|(?!\2)[^])*\2?)/g,sub:[{type:"oper",match:/^=/g}]},{type:"oper",match:/[\/!?]?>/g},{type:"class",match:RegExp(p,"g")}]},h=[{match:/<!--((?!-->)[^])*-->/g,sub:"todo"},{type:"class",match:/<!\[CDATA\[[\s\S]*?\]\]>/gi},e,{type:"str",match:RegExp(`<\\?${p}([^?]|\\?[^?>])*\\?+>`,"g"),sub:[{type:"var",match:RegExp(`^<\\?${p}`,"g"),sub:[{type:"oper",match:/^<\?/g}]},{type:"oper",match:/\?+>$/g}]},{type:"var",match:/&(#x?)?[\da-z]{1,8};/gi}];var o=[{type:"class",match:/<!DOCTYPE("[^"]*"|'[^']*'|[^"'>])*>/gi,sub:[{type:"str",match:/"[^"]*"|'[^']*'/g},{type:"oper",match:/^<!|>$/g},{type:"var",match:/DOCTYPE/gi}]},{match:RegExp(`<style${u}>((?!</style>)[^])*</style\\s*>`,"g"),sub:[{match:RegExp(`^<style${u}>`,"g"),sub:e.sub},{match:RegExp(`${e.match}|[^]*(?=</style\\s*>$)`,"g"),sub:"css"},e]},{match:RegExp(`<script${u}>((?!</script>)[^])*</script\\s*>`,"g"),sub:[{match:RegExp(`^<script${u}>`,"g"),sub:e.sub},{match:RegExp(`${e.match}|[^]*(?=</script\\s*>$)`,"g"),sub:"js"},e]},...h];
