var l=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var r=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var x=(t,e)=>{for(var d in e)l(t,d,{get:e[d],enumerable:!0})},b=(t,e,d,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of r(e))!u.call(t,a)&&a!==d&&l(t,a,{get:()=>e[a],enumerable:!(o=p(e,a))||o.enumerable});return t};var f=t=>b(l({},"__esModule",{value:!0}),t);var s={};x(s,{default:()=>n});module.exports=f(s);var n=[{expand:"strDouble"}];
