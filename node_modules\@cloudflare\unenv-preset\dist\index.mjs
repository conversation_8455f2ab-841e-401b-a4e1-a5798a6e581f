

// -- Unbuild CommonJS Shims --
import __cjs_url__ from 'url';
import __cjs_path__ from 'path';
import __cjs_mod__ from 'module';
const __filename = __cjs_url__.fileURLToPath(import.meta.url);
const __dirname = __cjs_path__.dirname(__filename);
const require = __cjs_mod__.createRequire(import.meta.url);
const version = "2.7.2";

const nativeModules = [
  "_stream_duplex",
  "_stream_passthrough",
  "_stream_readable",
  "_stream_transform",
  "_stream_writable",
  "_tls_common",
  "_tls_wrap",
  "assert",
  "assert/strict",
  "async_hooks",
  "buffer",
  "constants",
  "crypto",
  "diagnostics_channel",
  "dns",
  "dns/promises",
  "events",
  "net",
  "path",
  "path/posix",
  "path/win32",
  "querystring",
  "module",
  "stream",
  "stream/consumers",
  "stream/promises",
  "stream/web",
  "string_decoder",
  "sys",
  "timers",
  "timers/promises",
  "tls",
  "url",
  "util",
  "util/types",
  "zlib"
];
const hybridModules = ["console", "process"];
function getCloudflarePreset({
  compatibilityDate = "2024-09-03",
  compatibilityFlags = []
}) {
  const compat = {
    compatibilityDate,
    compatibilityFlags
  };
  const httpOverrides = getHttpOverrides(compat);
  const http2Overrides = getHttp2Overrides(compat);
  const osOverrides = getOsOverrides(compat);
  const fsOverrides = getFsOverrides(compat);
  const dynamicNativeModules = [
    ...nativeModules,
    ...httpOverrides.nativeModules,
    ...http2Overrides.nativeModules,
    ...osOverrides.nativeModules,
    ...fsOverrides.nativeModules
  ];
  const dynamicHybridModules = [
    ...hybridModules,
    ...httpOverrides.hybridModules,
    ...http2Overrides.hybridModules,
    ...osOverrides.hybridModules,
    ...fsOverrides.hybridModules
  ];
  return {
    meta: {
      name: "unenv:cloudflare",
      version,
      url: __filename
    },
    alias: {
      // `nodeCompatModules` are implemented in workerd.
      // Create aliases to override polyfills defined in based environments.
      ...Object.fromEntries(
        dynamicNativeModules.flatMap((p) => [
          [p, p],
          [`node:${p}`, `node:${p}`]
        ])
      ),
      // `hybridNodeCompatModules` are implemented by the cloudflare preset.
      ...Object.fromEntries(
        dynamicHybridModules.flatMap((m) => [
          [m, `@cloudflare/unenv-preset/node/${m}`],
          [`node:${m}`, `@cloudflare/unenv-preset/node/${m}`]
        ])
      )
    },
    inject: {
      // Setting symbols implemented by workerd to `false` so that `inject`s defined in base presets are not used.
      Buffer: false,
      global: false,
      clearImmediate: false,
      setImmediate: false,
      console: "@cloudflare/unenv-preset/node/console",
      process: "@cloudflare/unenv-preset/node/process"
    },
    polyfill: ["@cloudflare/unenv-preset/polyfill/performance"],
    external: dynamicNativeModules.flatMap((p) => [p, `node:${p}`])
  };
}
function getHttpOverrides({
  compatibilityDate,
  compatibilityFlags
}) {
  const httpDisabledByFlag = compatibilityFlags.includes(
    "disable_nodejs_http_modules"
  );
  const httpEnabledByFlag = compatibilityFlags.includes(
    "enable_nodejs_http_modules"
  );
  const httpEnabledByDate = compatibilityDate >= "2025-08-15";
  const httpEnabled = (httpEnabledByFlag || httpEnabledByDate) && !httpDisabledByFlag;
  if (!httpEnabled) {
    return { nativeModules: [], hybridModules: [] };
  }
  const httpServerEnabledByFlag = compatibilityFlags.includes(
    "enable_nodejs_http_server_modules"
  );
  const httpServerDisabledByFlag = compatibilityFlags.includes(
    "disable_nodejs_http_server_modules"
  );
  const httpServerEnabledByDate = compatibilityDate >= "2025-09-01";
  const httpServerEnabled = (httpServerEnabledByFlag || httpServerEnabledByDate) && !httpServerDisabledByFlag;
  return {
    nativeModules: [
      "_http_agent",
      "_http_client",
      "_http_common",
      "_http_incoming",
      "_http_outgoing",
      // `_http_server` can only be imported when the server flag is set
      // See https://github.com/cloudflare/workerd/blob/56efc04/src/workerd/api/node/node.h#L102-L106
      ...httpServerEnabled ? ["_http_server"] : [],
      "http",
      "https"
    ],
    hybridModules: []
  };
}
function getHttp2Overrides({
  compatibilityDate,
  compatibilityFlags
}) {
  const disabledByFlag = compatibilityFlags.includes(
    "disable_nodejs_http2_module"
  );
  const enabledByFlag = compatibilityFlags.includes(
    "enable_nodejs_http2_module"
  );
  const enabledByDate = compatibilityDate >= "2025-09-01";
  const enabled = (enabledByFlag || enabledByDate) && !disabledByFlag;
  return enabled ? {
    nativeModules: ["http2"],
    hybridModules: []
  } : {
    nativeModules: [],
    hybridModules: []
  };
}
function getOsOverrides({
  compatibilityDate,
  compatibilityFlags
}) {
  const disabledByFlag = compatibilityFlags.includes(
    "disable_nodejs_os_module"
  );
  const enabledByFlag = compatibilityFlags.includes("enable_nodejs_os_module");
  const enabledByDate = compatibilityDate >= "2025-09-15";
  const enabled = (enabledByFlag || enabledByDate) && !disabledByFlag;
  return enabled ? {
    nativeModules: ["os"],
    hybridModules: []
  } : {
    nativeModules: [],
    hybridModules: []
  };
}
function getFsOverrides({
  compatibilityDate,
  compatibilityFlags
}) {
  const disabledByFlag = compatibilityFlags.includes(
    "disable_nodejs_fs_module"
  );
  const enabledByFlag = compatibilityFlags.includes("enable_nodejs_fs_module");
  const enabledByDate = compatibilityDate >= "2025-09-15";
  const enabled = (enabledByFlag || enabledByDate) && !disabledByFlag;
  return enabled ? {
    nativeModules: ["fs/promises", "fs"],
    hybridModules: []
  } : {
    nativeModules: [],
    hybridModules: []
  };
}

const cloudflare = getCloudflarePreset({});

export { cloudflare, getCloudflarePreset };
