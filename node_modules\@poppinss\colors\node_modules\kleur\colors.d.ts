declare function print(input: string | boolean | number): string;
declare function print(input: undefined | void): undefined;
declare function print(input: null): null;
type Colorize = typeof print;

export declare const $: { enabled: boolean };

// Colors
export declare const black: Colorize;
export declare const red: Colorize;
export declare const green: Colorize;
export declare const yellow: Colorize;
export declare const blue: Colorize;
export declare const magenta: Colorize;
export declare const cyan: Colorize;
export declare const white: Colorize;
export declare const gray: Colorize;
export declare const grey: Colorize;

// Backgrounds
export declare const bgBlack: Colorize;
export declare const bgRed: Colorize;
export declare const bgGreen: Colorize;
export declare const bgYellow: Colorize;
export declare const bgBlue: Colorize;
export declare const bgMagenta: Colorize;
export declare const bgCyan: Colorize;
export declare const bgWhite: Colorize;

// Modifiers
export declare const reset: Colorize;
export declare const bold: Colorize;
export declare const dim: Colorize;
export declare const italic: Colorize;
export declare const underline: Colorize;
export declare const inverse: Colorize;
export declare const hidden: Colorize;
export declare const strikethrough: Colorize;
