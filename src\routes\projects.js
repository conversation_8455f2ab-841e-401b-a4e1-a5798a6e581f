import { Hono } from 'hono';
import { getProject, getProjectMilestones, getProjectUpdates, getProjectPhotos, getProjectDocuments, getProjectActions } from '../services/projectService';
import { authMiddleware } from '../middleware/auth';
import { getAccessToken } from '../services/procore-api';
import { fetchProCoreAPI } from '../services/api-utils';
import { mirrorDocumentToR2, mirrorPhotoToR2 } from '../services/r2-sync';

const app = new Hono();

// List projects (basic info) for dynamic refresh UI
app.get('/', async (c) => {
  try {
    const { results } = await c.env.DB.prepare(
      'SELECT external_id, name, status, synced_at, source_updated_at FROM projects ORDER BY source_updated_at DESC LIMIT 200'
    ).all();
    return c.json(results || []);
  } catch (error) {
    console.error('Error listing projects:', error);
    return c.json({ error: 'Failed to list projects' }, 500);
  }
});

// Get a specific project
app.get('/:id', async (c) => {
  const projectId = c.req.param('id');
  try {
    const project = await getProject(projectId, c.env);
    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }
    return c.json(project);
  } catch (error) {
    console.error(`Error fetching project ${projectId}:`, error);
    return c.json({ error: 'Failed to fetch project' }, 500);
  }
});

// Force refresh a project
app.post('/:id/refresh', async (c) => {
  const projectId = c.req.param('id');
  try {
    console.log(`Manual refresh requested for project ${projectId}`);
    
    // Force refresh project data
    const project = await getProject(projectId, c.env, null, true); // Force refresh
    if (!project) {
      return c.json({ error: 'Project not found' }, 404);
    }
    
    // Create an array to track all refresh operations
    const refreshOperations = [];
    
    // Refresh photos and mirror to R2
    const photosOperation = async () => {
      try {
        console.log(`Refreshing photos for project ${projectId}`);
        const photos = await getProjectPhotos(projectId, c.env, true); // Force refresh
        console.log(`Found ${photos.length} photos for project ${projectId}`);
        
        // Mirror each photo to R2
        if (photos && photos.length > 0) {
          for (const photo of photos) {
            try {
              await mirrorPhotoToR2(projectId, photo.external_id, c.env);
            } catch (photoError) {
              console.error(`Error mirroring photo ${photo.external_id} to R2:`, photoError);
            }
          }
        }
        return photos;
      } catch (error) {
        console.error(`Error refreshing photos for project ${projectId}:`, error);
        return [];
      }
    };
    
    // Refresh documents and mirror to R2
    const documentsOperation = async () => {
      try {
        console.log(`Refreshing documents for project ${projectId}`);
        const documents = await getProjectDocuments(projectId, c.env, true); // Force refresh
        console.log(`Found ${documents.length} documents for project ${projectId}`);
        
        // Mirror each document to R2
        if (documents && documents.length > 0) {
          for (const doc of documents) {
            try {
              await mirrorDocumentToR2(projectId, doc.external_id, c.env);
            } catch (docError) {
              console.error(`Error mirroring document ${doc.external_id} to R2:`, docError);
            }
          }
        }
        return documents;
      } catch (error) {
        console.error(`Error refreshing documents for project ${projectId}:`, error);
        return [];
      }
    };
    
    // Refresh updates
    const updatesOperation = async () => {
      try {
        console.log(`Refreshing updates for project ${projectId}`);
        const updates = await getProjectUpdates(projectId, c.env);
        console.log(`Found ${updates.length} updates for project ${projectId}`);
        return updates;
      } catch (error) {
        console.error(`Error refreshing updates for project ${projectId}:`, error);
        return [];
      }
    };
    
    // Add all operations to the array
    refreshOperations.push(photosOperation());
    refreshOperations.push(documentsOperation());
    refreshOperations.push(updatesOperation());
    
    // Wait for all refresh operations to complete to ensure D1 and R2 are in sync
    try {
      console.log(`Waiting for all refresh operations to complete for project ${projectId}`);
      await Promise.all(refreshOperations);
      console.log(`All refresh operations completed for project ${projectId}`);

      return c.json({
        status: 'success',
        message: 'Project data refreshed successfully (D1 and R2 in sync)',
        project
      });
    } catch (refreshError) {
      console.error(`Background refresh error for project ${projectId}:`, refreshError);

      // Still return success for the main project data, but note the sync issue
      return c.json({
        status: 'partial_success',
        message: 'Project data refreshed, but some files may not be fully synced to R2',
        project,
        sync_warning: refreshError.message
      });
    }
  } catch (error) {
    console.error(`Error refreshing project ${projectId}:`, error);
    return c.json({ error: 'Failed to refresh project data' }, 500);
  }
});

// Stream a project photo image, mirroring to R2 for caching
app.get('/:id/photos/:photoId/image', async (c) => {
  const projectId = c.req.param('id');
  const photoId = c.req.param('photoId');
  try {
    const token = await getAccessToken(c.env);
    if (!token) {
      return c.json({ error: 'Unauthorized: Missing Procore token' }, 401);
    }

    // Resolve company id (prefer exact project match)
    let companyId = 4276277;
    try {
      const list = await fetchProCoreAPI(`/rest/v1.1/projects?ids[]=${encodeURIComponent(projectId)}`, {}, c.env, token);
      if (Array.isArray(list)) {
        const match = list.find(p => p?.id?.toString() === projectId.toString());
        if (match?.company?.id) companyId = match.company.id;
      }
    } catch (_) {}

    const r2LegacyKey = `companies/${companyId}/projects/${projectId}/photos/${photoId}`; // old scheme (no extension)
    if (c.env.DOCS_BUCKET) {
      try {
        // Serve legacy object if present
        let obj = await c.env.DOCS_BUCKET.get(r2LegacyKey);
        if (obj) {
          const ctLegacy = String(obj.httpMetadata?.contentType || '').toLowerCase();
          if (ctLegacy === 'application/octet-stream' || ctLegacy === 'binary/octet-stream') {
            obj = null; // force re-fetch to correct type/extension
          }
        }
        if (!obj) {
          // Probe any new-scheme object by listing prefix
          const list = await c.env.DOCS_BUCKET.list({ prefix: `companies/${companyId}/projects/${projectId}/photos/${photoId}/`, limit: 5 });
          if (list && list.objects && list.objects.length > 0) {
            // Prefer non-.bin keys
            const preferred = list.objects.find(o => !o.key.endsWith('.bin')) || list.objects[0];
            if (preferred && preferred.key && !preferred.key.endsWith('.bin')) {
              obj = await c.env.DOCS_BUCKET.get(preferred.key);
            } else {
              obj = null; // only .bin found; force re-fetch to correct
            }
          }
        }
        if (obj) {
          // Migrate-on-read: copy to new key with filename+extension and correct metadata
          const ct = (obj.httpMetadata?.contentType || 'image/jpeg').toLowerCase();
          const extMap = { 'image/jpeg':'jpg','image/jpg':'jpg','image/png':'png','image/webp':'webp','image/heic':'heic','image/heif':'heif','image/gif':'gif','image/bmp':'bmp' };
          const ext = extMap[ct] || 'jpg';
          const base = `photo-${photoId}`;
          const filename = `${base}.${ext}`;
          const newKey = `companies/${companyId}/projects/${projectId}/photos/${photoId}/${filename}`;
          try {
            const [a, b] = obj.body.tee();
            const putPromise = c.env.DOCS_BUCKET.put(newKey, b, {
              httpMetadata: { contentType: ct, contentDisposition: `inline; filename="${filename}"`, cacheControl: 'public, max-age=86400' },
              customMetadata: { project_id: String(projectId), photo_id: String(photoId), company_id: String(companyId) }
            });
            try { c.executionCtx && c.executionCtx.waitUntil(putPromise); } catch (_) {}
            return new Response(a, {
              status: 200,
              headers: {
                'Content-Type': ct,
                'Content-Disposition': `inline; filename="${filename}"`,
                'Cache-Control': 'public, max-age=86400'
              }
            });
          } catch (_) {
            // Fallback: return as-is
            const cd = obj.httpMetadata?.contentDisposition || `inline; filename="photo-${photoId}.${ext}"`;
            return new Response(obj.body, {
              status: 200,
              headers: {
                'Content-Type': ct,
                'Content-Disposition': cd,
                'Cache-Control': 'public, max-age=86400'
              }
            });
          }
        }
      } catch (_) {}
    }

    // Lookup photo URL from D1; if missing/expired, refresh via fetchProjectPhotos
    let row = await c.env.DB.prepare('SELECT full_url, caption FROM photos WHERE project_id = ? AND external_id = ?')
      .bind(projectId, photoId)
      .first();
    if (!row || !row.full_url) {
      try {
        // Refresh photos for project to get fresh signed URLs
        await getProjectPhotos(projectId, c.env);
        row = await c.env.DB.prepare('SELECT full_url, caption FROM photos WHERE project_id = ? AND external_id = ?')
          .bind(projectId, photoId)
          .first();
      } catch (_) {}
    }
    const fullUrl = row?.full_url;
    if (!fullUrl) return c.json({ error: 'Photo not found' }, 404);

    // Build headers (some sandbox FAS URLs still require auth headers)
    const headers = { 'Accept': '*/*', 'Authorization': `Bearer ${token}`, 'Procore-Company-Id': companyId.toString() };
    if (c.env.PROCORE_APP_VERSION_KEY) {
      headers['Procore-App-Version'] = c.env.PROCORE_APP_VERSION_KEY;
      headers['Procore-App-Version-Key'] = c.env.PROCORE_APP_VERSION_KEY;
    }
    // Fetch FAS signed URL directly
    let res = await fetch(fullUrl, { headers, redirect: 'follow' });
    if (!res.ok && (res.status === 401 || res.status === 403)) {
      // Refresh signed URLs and retry once
      try {
        await getProjectPhotos(projectId, c.env);
        const row2 = await c.env.DB.prepare('SELECT full_url FROM photos WHERE project_id = ? AND external_id = ?')
          .bind(projectId, photoId)
          .first();
        if (row2?.full_url) {
          res = await fetch(row2.full_url, { headers, redirect: 'follow' });
        }
      } catch (_) {}
    }
    if (!res.ok) return c.json({ error: `Upstream fetch failed: ${res.status}` }, 502);

    let contentType = res.headers.get('Content-Type') || 'image/jpeg';
    if (contentType.toLowerCase() === 'application/octet-stream' || contentType.toLowerCase() === 'binary/octet-stream') {
      contentType = 'image/jpeg'; // sensible default for Photos
    }
    // Determine filename + extension per strategy
    const extMap = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp',
      'image/heic': 'heic',
      'image/heif': 'heif',
      'image/gif': 'gif',
      'image/bmp': 'bmp'
    };
    const ext = extMap[contentType.toLowerCase()] || 'jpg';
    const safeBase = (row?.caption && row.caption.trim()) ? row.caption.trim().replace(/[^A-Za-z0-9._-]+/g, '_') : `photo-${photoId}`;
    const filename = safeBase.endsWith(`.${ext}`) ? safeBase : `${safeBase}.${ext}`;
    const r2Key = `companies/${companyId}/projects/${projectId}/photos/${photoId}/${filename}`;
    if (c.env.DOCS_BUCKET && res.body) {
      try {
        const [a, b] = res.body.tee();
        const putPromise = c.env.DOCS_BUCKET.put(r2Key, b, {
          httpMetadata: { contentType, contentDisposition: `inline; filename="${filename}"`, cacheControl: 'public, max-age=86400' },
          customMetadata: { project_id: String(projectId), photo_id: String(photoId), company_id: String(companyId) }
        });
        try { c.executionCtx && c.executionCtx.waitUntil(putPromise); } catch (_) {}
        return new Response(a, {
          status: 200,
          headers: {
            'Content-Type': contentType,
            'Content-Disposition': `inline; filename="${filename}"`,
            'Cache-Control': 'public, max-age=86400'
          }
        });
      } catch (_) {}
    }
    return new Response(res.body, { status: 200, headers: { 'Content-Type': contentType, 'Content-Disposition': `inline; filename="${filename}"`, 'Cache-Control': 'public, max-age=86400' } });
  } catch (error) {
    console.error(`Error streaming photo for project ${projectId}, photo ${photoId}:`, error);
    return c.json({ error: 'Failed to stream photo' }, 500);
  }
});

// Get project milestones
app.get('/:id/milestones', async (c) => {
  const projectId = c.req.param('id');
  try {
    const milestones = await getProjectMilestones(projectId, c.env);
    return c.json(milestones);
  } catch (error) {
    console.error(`Error fetching milestones for project ${projectId}:`, error);
    return c.json({ error: 'Failed to fetch milestones' }, 500);
  }
});

// Get project updates
app.get('/:id/updates', async (c) => {
  const projectId = c.req.param('id');
  try {
    const updates = await getProjectUpdates(projectId, c.env);
    return c.json(updates);
  } catch (error) {
    console.error(`Error fetching updates for project ${projectId}:`, error);
    return c.json({ error: 'Failed to fetch updates' }, 500);
  }
});

// Get project photos
app.get('/:id/photos', async (c) => {
  const projectId = c.req.param('id');
  try {
    const photos = await getProjectPhotos(projectId, c.env);
    // Add CDN URL (Worker route) so clients can fetch mirrored images
    const withCdn = (photos || []).map(p => ({
      ...p,
      cdn_url: `/api/projects/${projectId}/photos/${p.external_id}/image`
    }));
    return c.json(withCdn);
  } catch (error) {
    console.error(`Error fetching photos for project ${projectId}:`, error);
    return c.json({ error: 'Failed to fetch photos' }, 500);
  }
});

// Get project documents
app.get('/:id/documents', async (c) => {
  const projectId = c.req.param('id');
  try {
    const documents = await getProjectDocuments(projectId, c.env);
    return c.json(documents);
  } catch (error) {
    console.error(`Error fetching documents for project ${projectId}:`, error);
    return c.json({ error: 'Failed to fetch documents' }, 500);
  }
});

// Get project customer actions
app.get('/:id/actions', async (c) => {
  const projectId = c.req.param('id');
  try {
    const actions = await getProjectActions(projectId, c.env);
    return c.json(actions);
  } catch (error) {
    console.error(`Error fetching actions for project ${projectId}:`, error);
    return c.json({ error: 'Failed to fetch actions' }, 500);
  }
});

// Check R2 sync status for a project
app.get('/:id/sync-status', async (c) => {
  const projectId = c.req.param('id');
  try {
    // Get D1 counts
    const photoCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM photos WHERE project_id = ?').bind(projectId).first();
    const docCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM documents WHERE project_id = ?').bind(projectId).first();

    // Get R2 counts (approximate - check for non-.bin files)
    let r2PhotoCount = 0;
    let r2DocCount = 0;

    if (c.env.DOCS_BUCKET) {
      try {
        // Get company ID for this project to build correct R2 paths
        const token = await getAccessToken(c.env);
        let companyId = 4276277; // Default
        if (token) {
          try {
            const list = await fetchProCoreAPI(`/rest/v1.1/projects?ids[]=${encodeURIComponent(projectId)}`, {}, c.env, token);
            if (Array.isArray(list)) {
              const match = list.find(p => p?.id?.toString() === projectId.toString());
              if (match?.company?.id) companyId = match.company.id;
            }
          } catch (_) {}
        }

        // Count photos in R2 with correct company ID
        const photoPrefix = `companies/${companyId}/projects/${projectId}/photos/`;
        const photoList = await c.env.DOCS_BUCKET.list({ prefix: photoPrefix, limit: 1000 });
        r2PhotoCount = (photoList?.objects || []).filter(obj => !obj.key.endsWith('.bin')).length;

        // Count documents in R2 with correct company ID
        const docPrefix = `companies/${companyId}/projects/${projectId}/documents/`;
        const docList = await c.env.DOCS_BUCKET.list({ prefix: docPrefix, limit: 1000 });
        r2DocCount = (docList?.objects || []).filter(obj => !obj.key.endsWith('.bin')).length;

        console.log(`R2 sync status check for project ${projectId}: photos=${r2PhotoCount}, docs=${r2DocCount}, companyId=${companyId}`);
      } catch (r2Error) {
        console.error(`Error checking R2 for project ${projectId}:`, r2Error);
      }
    }

    const photosSynced = r2PhotoCount >= (photoCount?.count || 0);
    const docsSynced = r2DocCount >= (docCount?.count || 0);
    const fullySynced = photosSynced && docsSynced;

    return c.json({
      project_id: projectId,
      fully_synced: fullySynced,
      d1_counts: {
        photos: photoCount?.count || 0,
        documents: docCount?.count || 0
      },
      r2_counts: {
        photos: r2PhotoCount,
        documents: r2DocCount
      },
      sync_status: {
        photos_synced: photosSynced,
        documents_synced: docsSynced
      }
    });
  } catch (error) {
    console.error(`Error checking sync status for project ${projectId}:`, error);
    return c.json({ error: 'Failed to check sync status' }, 500);
  }
});

export const projectRoutes = app;

// Secure download proxy for documents
app.use('/:id/documents/:docId/download', authMiddleware);
app.get('/:id/documents/:docId/download', async (c) => {
  const projectId = c.req.param('id');
  const rawDocId = c.req.param('docId');
  // Normalize IDs like "13831256.0" -> "13831256"
  const docId = String(rawDocId).includes('.') ? String(rawDocId).split('.')[0] : String(rawDocId);
  try {
    const token = await getAccessToken(c.env);
    if (!token) {
      return c.json({ error: 'Unauthorized: Missing Procore token' }, 401);
    }

    // Resolve company id (prefer exact project match)
    let companyId = 4276277;
    try {
      const list = await fetchProCoreAPI(`/rest/v1.1/projects?ids[]=${encodeURIComponent(projectId)}`, {}, c.env, token);
      if (Array.isArray(list)) {
        const match = list.find(p => p?.id?.toString() === projectId.toString());
        if (match?.company?.id) companyId = match.company.id;
      }
    } catch (_) {}

    // Preload D1 row for better filename and potential display_url fallback
    const d1Row = await c.env.DB.prepare('SELECT display_url, title FROM documents WHERE project_id = ? AND external_id = ?')
      .bind(projectId, docId)
      .first();

    // R2: if object exists, stream immediately (support legacy key w/o extension and new scheme with filename)
    const r2LegacyKey = `companies/${companyId}/projects/${projectId}/documents/${docId}`;
    if (c.env.DOCS_BUCKET) {
      try {
        let obj = await c.env.DOCS_BUCKET.get(r2LegacyKey);
        if (obj) {
          const ctLegacy = String(obj.httpMetadata?.contentType || '').toLowerCase();
          if (ctLegacy === 'application/octet-stream' || ctLegacy === 'binary/octet-stream') {
            obj = null; // force re-fetch to correct type/extension
          }
        }
        if (!obj) {
          const list = await c.env.DOCS_BUCKET.list({ prefix: `companies/${companyId}/projects/${projectId}/documents/${docId}/`, limit: 10 });
          if (list && list.objects && list.objects.length > 0) {
            // Prefer non-.bin keys
            const preferred = list.objects.find(o => !o.key.endsWith('.bin')) || null;
            if (preferred && preferred.key && !preferred.key.endsWith('.bin')) {
              obj = await c.env.DOCS_BUCKET.get(preferred.key);
              // If stored with octet-stream, treat as missing to force a correct re-fetch/mirror
              if (obj) {
                const ctStored = String(obj.httpMetadata?.contentType || '').toLowerCase();
                if (ctStored === 'application/octet-stream' || ctStored === 'binary/octet-stream') {
                  obj = null;
                }
              }
            } else {
              obj = null; // only .bin present -> re-fetch
            }
          }
        }
        if (obj) {
          const ct = obj.httpMetadata?.contentType || 'application/octet-stream';
          const cd = obj.httpMetadata?.contentDisposition || `attachment; filename="${(d1Row?.title || `document-${docId}`).replace(/"/g, '')}"`;
          // Cache PDFs/images longer, others shorter
          const cache = (ct.startsWith('image/') || ct === 'application/pdf') ? 'public, max-age=86400' : 'no-store';
          return new Response(obj.body, {
            status: 200,
            headers: {
              'Content-Type': ct,
              'Content-Disposition': cd,
              'Cache-Control': cache
            }
          });
        }
      } catch (_) {}
    }

    // Try Procore download endpoints then fallback to metadata then stored display_url
    const base = c.env.PROCORE_API_BASE_URL || 'https://api.procore.com';
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Accept': '*/*',
      'Procore-Company-Id': companyId.toString(),
    };
    // Some Procore endpoints require the app version key header
    if (c.env.PROCORE_APP_VERSION_KEY) {
      headers['Procore-App-Version'] = c.env.PROCORE_APP_VERSION_KEY;
      headers['Procore-App-Version-Key'] = c.env.PROCORE_APP_VERSION_KEY;
    }

    async function streamAndMaybeMirror(res, fallbackName) {
      // If R2 is available, tee the body and mirror asynchronously (use filename+ext)
      const contentType = (res.headers.get('Content-Type') || 'application/octet-stream').toLowerCase();
      const baseName = (fallbackName || d1Row?.title || `document-${docId}`).replace(/"/g, '');
      // Decide extension by content-type if missing
      const extByCT = {
        'application/pdf': 'pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
        'application/msword': 'doc',
        'application/vnd.ms-excel': 'xls',
        'application/vnd.ms-powerpoint': 'ppt',
        'image/jpeg': 'jpg',
        'image/jpg': 'jpg',
        'image/png': 'png',
        'image/webp': 'webp',
        'video/mp4': 'mp4',
        'video/quicktime': 'mov',
        'application/zip': 'zip'
      };
      const hasExt = /\.[A-Za-z0-9]{2,5}$/.test(baseName);
      const ext = hasExt ? '' : (extByCT[contentType] ? `.${extByCT[contentType]}` : '');
      const filename = `${baseName}${ext}`;
      const isInline = contentType.startsWith('image/') || contentType === 'application/pdf' || contentType.startsWith('video/');
      const contentDisposition = `"${filename}"`;
      const cdHeader = `${isInline ? 'inline' : 'attachment'}; filename=${contentDisposition}`;
      const cache = contentType === 'application/pdf' || contentType.startsWith('image/') ? 'public, max-age=86400' : (contentType.startsWith('video/') ? 'public, max-age=3600' : 'no-store');
      if (c.env.DOCS_BUCKET && res.body) {
        try {
          const [a, b] = res.body.tee();
          const r2KeyNew = `companies/${companyId}/projects/${projectId}/documents/${docId}/${filename}`;
          const putPromise = c.env.DOCS_BUCKET.put(r2KeyNew, b, {
            httpMetadata: { contentType, contentDisposition: cdHeader, cacheControl: cache },
            customMetadata: { project_id: String(projectId), doc_id: String(docId), company_id: String(companyId) }
          });
          // Use waitUntil if available to avoid delaying response
          try { c.executionCtx && c.executionCtx.waitUntil(putPromise); } catch (_) {}
          return new Response(a, {
            status: 200,
            headers: {
              'Content-Type': contentType,
              'Content-Disposition': cdHeader,
              'Cache-Control': cache,
            }
          });
        } catch (_) {
          // Fall through to direct streaming without mirroring
        }
      }
      return new Response(res.body, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': cdHeader,
          'Cache-Control': cache,
        }
      });
    }

    async function tryDownload(path) {
      // Ensure company_id is present for endpoints that require it
      let urlPath = /[?&]company_id=/.test(path)
        ? path
        : `${path}${path.includes('?') ? '&' : '?'}company_id=${encodeURIComponent(companyId)}`;
      // Some file endpoints require project_id
      if (urlPath.includes('/files/') || urlPath.includes('/file_versions/')) {
        urlPath += /[?&]project_id=/.test(urlPath)
          ? ''
          : `${urlPath.includes('?') ? '&' : '?'}project_id=${encodeURIComponent(projectId)}`;
      }
      const url = `${base}${urlPath}`;
      const res = await fetch(url, { headers, redirect: 'follow' });
      if (!res.ok) throw new Error(`Procore download failed: ${res.status} ${res.statusText}`);
      return streamAndMaybeMirror(res);
    }

    // Candidate endpoints to attempt in order
    const candidates = [
      `/rest/v1.0/projects/${encodeURIComponent(projectId)}/documents/${encodeURIComponent(docId)}/download`,
      `/rest/v2.0/projects/${encodeURIComponent(projectId)}/documents/${encodeURIComponent(docId)}/download`,
      `/rest/v1.0/documents/${encodeURIComponent(docId)}/download`,
      `/rest/v1.0/files/${encodeURIComponent(docId)}/download`,
      `/rest/v1.0/companies/${encodeURIComponent(companyId)}/documents/${encodeURIComponent(docId)}/download`,
      `/rest/v1.0/companies/${encodeURIComponent(companyId)}/files/${encodeURIComponent(docId)}/download`,
      `/rest/v1.0/companies/${encodeURIComponent(companyId)}/projects/${encodeURIComponent(projectId)}/documents/${encodeURIComponent(docId)}/download`
    ];

    for (const path of candidates) {
      try {
        return await tryDownload(path);
      } catch (e) {
        // continue to next
      }
    }

    // Fallback: fetch metadata to obtain a download or web URL
    try {
      const metaPaths = [
        `/rest/v1.0/documents/${encodeURIComponent(docId)}`,
        `/rest/v2.0/projects/${encodeURIComponent(projectId)}/documents/${encodeURIComponent(docId)}`,
        `/rest/v1.1/companies/${encodeURIComponent(companyId)}/documents/${encodeURIComponent(docId)}`,
        // Files metadata endpoints often require project_id explicitly
        `/rest/v1.0/files/${encodeURIComponent(docId)}?project_id=${encodeURIComponent(projectId)}`,
        `/rest/v1.0/companies/${encodeURIComponent(companyId)}/files/${encodeURIComponent(docId)}?project_id=${encodeURIComponent(projectId)}`,
        `/rest/v1.1/companies/${encodeURIComponent(companyId)}/files/${encodeURIComponent(docId)}?project_id=${encodeURIComponent(projectId)}`
      ];
      for (const m of metaPaths) {
        try {
          const meta = await fetchProCoreAPI(m, {}, c.env, token, companyId);
          const url = meta?.download_url || meta?.url || meta?.web_url || meta?.direct_download_url;
          if (url) {
            const res = await fetch(url, { headers, redirect: 'follow' });
            if (res.ok) {
              const ct = res.headers.get('Content-Type') || '';
              // If Procore responds with JSON containing a signed URL, follow it
              if (ct.includes('application/json')) {
                try {
                  const j = await res.clone().json();
                  const dl = j?.download_url || j?.url || j?.web_url;
                  if (dl) {
                    const res2 = await fetch(dl, { redirect: 'follow' });
                    if (res2.ok) {
                      const name = meta?.title || meta?.name || `document-${docId}`;
                      return await streamAndMaybeMirror(res2, name);
                    }
                  }
                } catch (_) {}
              }
              const name = meta?.title || meta?.name || `document-${docId}`;
              return await streamAndMaybeMirror(res, name);
            }
          }
          // If metadata includes file_versions, try their URLs and version download endpoints
          if (Array.isArray(meta?.file_versions) && meta.file_versions.length > 0) {
            for (const v of meta.file_versions) {
              const vUrl = v?.download_url || v?.url;
              if (vUrl) {
                try {
                  const resV = await fetch(vUrl, { headers, redirect: 'follow' });
                  if (resV.ok) {
                    const ct = resV.headers.get('Content-Type') || '';
                    if (ct.includes('application/json')) {
                      try {
                        const j2 = await resV.clone().json();
                        const dl2 = j2?.download_url || j2?.url || j2?.web_url;
                        if (dl2) {
                          const resV2 = await fetch(dl2, { redirect: 'follow' });
                          if (resV2.ok) {
                            const name = meta?.title || meta?.name || `document-${docId}`;
                            return await streamAndMaybeMirror(resV2, name);
                          }
                        }
                      } catch (_) {}
                    }
                    const name = meta?.title || meta?.name || `document-${docId}`;
                    return await streamAndMaybeMirror(resV, name);
                  }
                } catch (_) {}
              }
              if (v?.id) {
                try {
                  return await tryDownload(`/rest/v1.0/file_versions/${encodeURIComponent(v.id)}/download`);
                } catch (_) {}
              }
            }
          }
          // If metadata includes a file id, try downloading by file id
          const fileId = meta?.file_id || meta?.file?.id || meta?.fileId;
          if (fileId) {
            try {
              return await tryDownload(`/rest/v1.0/files/${encodeURIComponent(fileId)}/download`);
            } catch (_) {}
          }
        } catch (_) {}
      }
    } catch (_) {}

    // Final fallback: stream from stored display_url if present
    if (d1Row && d1Row.display_url) {
      const res = await fetch(d1Row.display_url, { redirect: 'follow' });
      if (res.ok) {
        return await streamAndMaybeMirror(res, d1Row.title);
      }
    }

    return c.json({ error: 'Download not available' }, 404);
  } catch (error) {
    console.error(`Error proxying document download for project ${projectId}, doc ${docId}:`, error);
    return c.json({ error: 'Failed to download document' }, 500);
  }
});
