var e=Object.defineProperty;var w=Object.getOwnPropertyDescriptor;var g=Object.getOwnPropertyNames;var h=Object.prototype.hasOwnProperty;var y=(m,t)=>{for(var c in t)e(m,c,{get:t[c],enumerable:!0})},o=(m,t,c,p)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of g(t))!h.call(m,a)&&a!==c&&e(m,a,{get:()=>t[a],enumerable:!(p=w(t,a))||p.enumerable});return m};var u=m=>o(e({},"__esModule",{value:!0}),m);var r={};y(r,{default:()=>d});module.exports=u(r);var d=[{match:/^#.*/gm,sub:"todo"},{type:"class",match:/^\w+(?=:?)/gm},{type:"num",match:/:\d+/g},{type:"oper",match:/[:/&?]|\w+=/g},{type:"func",match:/[.\w]+@|#[\w]+$/gm},{type:"var",match:/\w+\.\w+(\.\w+)*/g}];
