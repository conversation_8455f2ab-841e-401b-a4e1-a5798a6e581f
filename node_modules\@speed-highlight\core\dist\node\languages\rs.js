var s=Object.defineProperty;var c=Object.getOwnPropertyDescriptor;var o=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var p=(t,e)=>{for(var r in e)s(t,r,{get:e[r],enumerable:!0})},i=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of o(e))!u.call(t,a)&&a!==r&&s(t,a,{get:()=>e[a],enumerable:!(n=c(e,a))||n.enumerable});return t};var m=t=>i(s({},"__esModule",{value:!0}),t);var f={};p(f,{default:()=>l});module.exports=m(f);var l=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/\b(as|break|const|continue|crate|else|enum|extern|false|fn|for|if|impl|in|let|loop|match|mod|move|mut|pub|ref|return|self|Self|static|struct|super|trait|true|type|unsafe|use|where|while|async|await|dyn|abstract|become|box|do|final|macro|override|priv|typeof|unsized|virtual|yield|try)\b/g},{type:"oper",match:/[/*+:?&|%^~=!,<>.^-]+/g},{type:"class",match:/\b[A-Z][\w_]*\b/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*!?\s*\()/g}];
