import { fetchFromProCore, getAccessToken } from './procore-api';
import { fetchProCoreAPI } from './api-utils';
import { getProject, getProjectUpdates, getProjectPhotos, getProjectDocuments } from './projectService';
import { handleCompanyFileEvent, handleCompanyFolderEvent } from './companyFileHandlers';

/**
 * Process a webhook event from ProCore
 * @param {object} payload - The webhook payload
 * @param {object} env - Environment variables and bindings
 * @returns {Promise<object>} Processing result
 */
export async function processWebhook(payload, env) {
  try {
    // Log the webhook event for troubleshooting
    await logWebhookEvent(payload, env);

    // Extract event information with fallbacks for different payload structures
    const resource_name = payload?.resource_name || payload?.resource || payload?.model;
    const event_type = payload?.event_type || payload?.event || payload?.action;
    const resource_id = payload?.resource_id || payload?.id || payload?.object_id;
    const project_id = payload?.project_id || payload?.project?.id || payload?.p;
    
    // Process based on resource type
    switch (resource_name) {
      case 'Project':
        return await handleProjectEvent(resource_id, event_type, env);
      case 'Projects':
        return await handleProjectEvent(resource_id, event_type, env, payload.company_id);
      
      case 'DailyLog':
        return await handleDailyLogEvent(resource_id, project_id, event_type, env);
      
      case 'Photo':
        return await handlePhotoEvent(resource_id, project_id, event_type, env);
      
      // Standard document/file types
      case 'Document':
        return await handleDocumentEvent(resource_id, project_id, event_type, env);
      case 'Documents':
        return await handleDocumentEvent(resource_id, project_id, event_type, env);
      case 'File':
        return await handleDocumentEvent(resource_id, project_id, event_type, env);
      case 'Files':
        return await handleDocumentEvent(resource_id, project_id, event_type, env);
      
      // Company-level file events
      case 'Company File Versions':
        console.log('Processing Company File Versions event');
        return await handleCompanyFileEvent(resource_id, payload.company_id, event_type, env);
      case 'Company Files':
        console.log('Processing Company Files event');
        return await handleCompanyFileEvent(resource_id, payload.company_id, event_type, env);
      case 'Company Folders':
        console.log('Processing Company Folders event');
        return await handleCompanyFolderEvent(resource_id, payload.company_id, event_type, env);
      
      default:
        console.log(`Ignoring webhook for unsupported resource type: ${resource_name}`);
        return { status: 'ignored', reason: 'Unsupported resource type' };
    }
  } catch (error) {
    console.error(`Error processing webhook: ${error.message}`);
    throw error;
  }
}

/**
 * Handle a project event
 * @param {string} resourceId - The project ID
 * @param {string} eventType - The event type (created, updated, deleted)
 * @param {object} env - Environment variables and bindings
 * @param {string} [companyId] - Optional company ID for company-level events
 * @returns {Promise<object>} Processing result
 */
async function handleProjectEvent(resourceId, eventType, env, companyId) {
  try {
    if (eventType === 'deleted') {
      // Mark project as inactive in our database
      await env.DB.prepare(
        'UPDATE projects SET status = "inactive", synced_at = ? WHERE external_id = ?'
      ).bind(new Date().toISOString(), resourceId).run();
      
      return { status: 'success', action: 'project_marked_inactive' };
    } else {
      // Log the specific event type for debugging
      console.log(`Processing ${eventType} event for project ${resourceId}${companyId ? ` in company ${companyId}` : ''}`);
      
      if (eventType === 'create') {
        // For newly created projects, fetch and store complete project data
        console.log(`New project created: ${resourceId}`);
        await getProject(resourceId, env, companyId);
        return { status: 'success', action: 'project_created' };
      } else if (eventType === 'update') {
        // For updated projects, fetch and update project data
        console.log(`Project updated: ${resourceId}`);
        await getProject(resourceId, env, companyId);
        return { status: 'success', action: 'project_updated' };
      } else {
        // For other event types, just fetch and update project data
        await getProject(resourceId, env, companyId);
        return { status: 'success', action: 'project_data_refreshed' };
      }
    }
  } catch (error) {
    // Handle 404 errors gracefully for test data or non-existent resources
    if (error.message && error.message.includes('404')) {
      console.log(`Project ${resourceId} not found, possibly test data or deleted resource`);
      // Log the event but don't fail the webhook processing
      await env.DB.prepare(`
        INSERT INTO webhook_events (
          resource_name, event_type, resource_id, project_id,
          payload, received_at
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).bind(
        'Project', 'not_found', resourceId, resourceId,
        JSON.stringify({ error: 'Resource not found in ProCore' }), new Date().toISOString()
      ).run();
      return { status: 'success', action: 'project_not_found' };
    }
    console.error(`Error handling project event: ${error.message}`);
    throw error;
  }
}

/**
 * Handle a daily log event
 * @param {string} resourceId - The daily log ID
 * @param {string} projectId - The project ID
 * @param {string} eventType - The event type (created, updated, deleted)
 * @param {object} env - Environment variables and bindings
 * @returns {Promise<object>} Processing result
 */
async function handleDailyLogEvent(resourceId, projectId, eventType, env) {
  try {
    if (eventType === 'deleted') {
      // Remove the update from our database
      await env.DB.prepare(
        'DELETE FROM updates WHERE external_id = ?'
      ).bind(resourceId).run();
      
      return { status: 'success', action: 'update_deleted' };
    } else {
      // Fetch the specific daily log
      const dailyLog = await fetchFromProCore(`/projects/${projectId}/daily_logs/${resourceId}`, env);
      
      // Check if it should be visible to customers
      if (dailyLog.is_customer_visible || isInCustomerFolder(dailyLog)) {
        // Transform and store the update
        const update = {
          external_id: dailyLog.id,
          project_id: projectId,
          date: dailyLog.date,
          text: sanitizeText(dailyLog.notes),
          is_customer_visible: true,
          milestone_tag: getMilestoneTagFromUpdate(dailyLog),
          synced_at: new Date().toISOString(),
          source_updated_at: dailyLog.updated_at
        };
        
        await storeUpdate(update, env);
        return { status: 'success', action: 'update_synced' };
      } else {
        // Not customer visible, remove if it exists
        await env.DB.prepare(
          'DELETE FROM updates WHERE external_id = ?'
        ).bind(resourceId).run();
        
        return { status: 'success', action: 'update_filtered_out' };
      }
    }
  } catch (error) {
    console.error(`Error handling daily log event: ${error.message}`);
    throw error;
  }
}

/**
 * Handle a photo event
 * @param {string} resourceId - The photo ID
 * @param {string} projectId - The project ID
 * @param {string} eventType - The event type (created, updated, deleted)
 * @param {object} env - Environment variables and bindings
 * @returns {Promise<object>} Processing result
 */
async function handlePhotoEvent(resourceId, projectId, eventType, env) {
  try {
    if (eventType === 'deleted') {
      // Remove the photo from our database
      await env.DB.prepare(
        'DELETE FROM photos WHERE external_id = ?'
      ).bind(resourceId).run();
      // Best-effort: delete mirrored objects from R2
      try { await deletePhotoFromR2(projectId, resourceId, env); } catch (_) {}
      return { status: 'success', action: 'photo_deleted' };
    } else {
      try {
        // Fetch the specific photo
        const photo = await fetchFromProCore(`/projects/${projectId}/photos/${resourceId}`, env);
        
        // Check if it should be visible to customers
        if (photo.is_customer_visible || isInCustomerAlbum(photo)) {
          // Transform and store the photo
          const photoData = {
            external_id: photo.id,
            project_id: projectId,
            date: photo.taken_at || photo.created_at,
            caption: sanitizeText(photo.caption),
            thumbnail_url: photo.thumbnail_url,
            full_url: photo.url,
            is_customer_visible: true,
            milestone_tag: null, // Will be set by admin later
            synced_at: new Date().toISOString(),
            source_updated_at: photo.updated_at
          };
          
          await storePhoto(photoData, env);
          // Mirror to R2 in background style
          try { await mirrorPhotoToR2(projectId, resourceId, env); } catch (_) {}
          return { status: 'success', action: 'photo_synced' };
        } else {
          // Not customer visible, remove if it exists
          await env.DB.prepare(
            'DELETE FROM photos WHERE external_id = ?'
          ).bind(resourceId).run();
          
          return { status: 'success', action: 'photo_filtered_out' };
        }
      } catch (error) {
        // Handle 404 errors gracefully for test data or non-existent resources
        if (error.message && error.message.includes('404')) {
          console.log(`Photo ${resourceId} not found in project ${projectId}, possibly test data or deleted resource`);
          // Log the event but don't fail the webhook processing
          await env.DB.prepare(`
            INSERT INTO webhook_events (
              resource_name, event_type, resource_id, project_id,
              payload, received_at
            ) VALUES (?, ?, ?, ?, ?, ?)
          `).bind(
            'Photo', 'not_found', resourceId, projectId,
            JSON.stringify({ error: 'Resource not found in ProCore' }), new Date().toISOString()
          ).run();
          return { status: 'success', action: 'photo_not_found' };
        }
        throw error; // Re-throw other errors
      }
    }
  } catch (error) {
    console.error(`Error handling photo event: ${error.message}`);
    throw error;
  }
}

/**
 * Handle a document event
 * @param {string} resourceId - The document ID
 * @param {string} projectId - The project ID
 * @param {string} eventType - The event type (created, updated, deleted)
 * @param {object} env - Environment variables and bindings
 * @returns {Promise<object>} Processing result
 */
async function handleDocumentEvent(resourceId, projectId, eventType, env) {
  try {
    if (eventType === 'deleted') {
      // Remove the document from our database
      await env.DB.prepare(
        'DELETE FROM documents WHERE external_id = ?'
      ).bind(resourceId).run();
      // Best-effort: delete mirrored objects from R2
      try { await deleteDocumentFromR2(projectId, resourceId, env); } catch (_) {}
      return { status: 'success', action: 'document_deleted' };
    } else {
      try {
        // Fetch the specific document using robust metadata resolution
        const meta = await fetchDocumentMeta(projectId, resourceId, env);

        if (meta) {
          // Decide visibility (default to visible)
          const visible = meta.is_customer_visible || isInCustomerFolder(meta) || true;
          if (visible) {
            const title = meta.title || meta.name || meta.filename || meta.file_name || `Document ${resourceId}`;
            const displayUrl = meta.url || meta.download_url || meta.web_url || meta.direct_download_url || '';
            const fileType = getFileType(title);
            const documentData = {
              external_id: String(meta.id || resourceId),
              project_id: String(projectId),
              title,
              file_type: fileType,
              display_url: displayUrl,
              is_customer_visible: 1,
              download_allowed: displayUrl ? 1 : 0,
              synced_at: new Date().toISOString(),
              source_updated_at: meta.updated_at || new Date().toISOString()
            };
            await storeDocument(documentData, env);
            // Proactively mirror to R2 (best-effort)
            try { await mirrorDocumentToR2(projectId, resourceId, env); } catch (_) {}
            return { status: 'success', action: 'document_synced' };
          } else {
            await env.DB.prepare('DELETE FROM documents WHERE external_id = ?').bind(resourceId).run();
            return { status: 'success', action: 'document_filtered_out' };
          }
        }
        // If we could not fetch metadata, still succeed but note the condition
        return { status: 'success', action: 'document_received_no_metadata' };
      } catch (error) {
        // Handle 404 errors gracefully for test data or non-existent resources
        if (error.message && error.message.includes('404')) {
          console.log(`Document ${resourceId} not found in project ${projectId}, possibly test data or deleted resource`);
          // Log the event but don't fail the webhook processing
          await env.DB.prepare(`
            INSERT INTO webhook_events (
              resource_name, event_type, resource_id, project_id,
              payload, received_at
            ) VALUES (?, ?, ?, ?, ?, ?)
          `).bind(
            'Document', 'not_found', resourceId, projectId,
            JSON.stringify({ error: 'Resource not found in ProCore' }), new Date().toISOString()
          ).run();
          return { status: 'success', action: 'document_not_found' };
        }
        throw error; // Re-throw other errors
      }
    }
  } catch (error) {
    console.error(`Error handling document event: ${error.message}`);
    throw error;
  }
}

// Fetch document metadata using multiple API paths; return best match or null
async function fetchDocumentMeta(projectId, docId, env) {
  try {
    const token = await getAccessToken(env);
    const companyId = await resolveCompanyId(projectId, env, token);
    const paths = [
      `/rest/v1.0/files/${encodeURIComponent(docId)}?project_id=${encodeURIComponent(projectId)}`,
      `/rest/v1.0/documents/${encodeURIComponent(docId)}`,
      `/rest/v2.0/projects/${encodeURIComponent(projectId)}/documents/${encodeURIComponent(docId)}`,
      `/rest/v1.0/projects/${encodeURIComponent(projectId)}/documents/${encodeURIComponent(docId)}`
    ];
    for (const p of paths) {
      try {
        const meta = await fetchProCoreAPI(p, {}, env, token, companyId);
        if (meta && (meta.id || meta.filename || meta.title || meta.download_url || meta.url)) return meta;
      } catch (_) {}
    }
    return null;
  } catch (e) {
    return null;
  }
}

/**
 * Log a webhook event for troubleshooting
 * @param {object} payload - The webhook payload
 * @param {object} env - Environment variables and bindings
 */
async function logWebhookEvent(payload, env) {
  try {
    // Extract fields with fallbacks for different payload structures
    const resource_name = payload?.resource_name || payload?.resource || payload?.model || 'Unknown';
    const event_type = payload?.event_type || payload?.event || payload?.action || 'Unknown';
    const resource_id = payload?.resource_id || payload?.id || payload?.object_id || '-';
    const project_id = payload?.project_id || payload?.project?.id || payload?.p || '-';

    await env.DB.prepare(`
      INSERT INTO webhook_events (
        resource_name, event_type, resource_id, project_id,
        payload, received_at
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      resource_name,
      event_type,
      resource_id,
      project_id,
      JSON.stringify(payload),
      new Date().toISOString()
    ).run();
  } catch (error) {
    console.error(`Error logging webhook event: ${error.message}`);
    // Don't throw here, just log the error
  }
}

/**
 * Store a single update in the database
 * @param {object} update - The update data
 * @param {object} env - Environment variables and bindings
 */
async function storeUpdate(update, env) {
  try {
    await env.DB.prepare(`
      INSERT INTO updates (
        external_id, project_id, date, text, 
        is_customer_visible, milestone_tag,
        synced_at, source_updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      ON CONFLICT (external_id) DO UPDATE SET
        date = excluded.date,
        text = excluded.text,
        is_customer_visible = excluded.is_customer_visible,
        milestone_tag = excluded.milestone_tag,
        synced_at = excluded.synced_at,
        source_updated_at = excluded.source_updated_at
    `).bind(
      update.external_id,
      update.project_id,
      update.date,
      update.text,
      update.is_customer_visible ? 1 : 0,
      update.milestone_tag,
      update.synced_at,
      update.source_updated_at
    ).run();
  } catch (error) {
    console.error(`Error in storeUpdate: ${error.message}`);
    throw error;
  }
}

/**
 * Store a single photo in the database
 * @param {object} photo - The photo data
 * @param {object} env - Environment variables and bindings
 */
async function storePhoto(photo, env) {
  try {
    await env.DB.prepare(`
      INSERT INTO photos (
        external_id, project_id, date, caption, 
        thumbnail_url, full_url, is_customer_visible, milestone_tag,
        synced_at, source_updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON CONFLICT (external_id) DO UPDATE SET
        date = excluded.date,
        caption = excluded.caption,
        thumbnail_url = excluded.thumbnail_url,
        full_url = excluded.full_url,
        is_customer_visible = excluded.is_customer_visible,
        milestone_tag = excluded.milestone_tag,
        synced_at = excluded.synced_at,
        source_updated_at = excluded.source_updated_at
    `).bind(
      photo.external_id,
      photo.project_id,
      photo.date,
      photo.caption,
      photo.thumbnail_url,
      photo.full_url,
      photo.is_customer_visible ? 1 : 0,
      photo.milestone_tag,
      photo.synced_at,
      photo.source_updated_at
    ).run();
  } catch (error) {
    console.error(`Error in storePhoto: ${error.message}`);
    throw error;
  }
}

/**
 * Store a single document in the database
 * @param {object} document - The document data
 * @param {object} env - Environment variables and bindings
 */
async function storeDocument(document, env) {
  try {
    await env.DB.prepare(`
      INSERT INTO documents (
        external_id, project_id, title, file_type, 
        display_url, is_customer_visible, download_allowed,
        synced_at, source_updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON CONFLICT (external_id) DO UPDATE SET
        title = excluded.title,
        file_type = excluded.file_type,
        display_url = excluded.display_url,
        is_customer_visible = excluded.is_customer_visible,
        download_allowed = excluded.download_allowed,
        synced_at = excluded.synced_at,
        source_updated_at = excluded.source_updated_at
    `).bind(
      document.external_id,
      document.project_id,
      document.title,
      document.file_type,
      document.display_url,
      document.is_customer_visible ? 1 : 0,
      document.download_allowed ? 1 : 0,
      document.synced_at,
      document.source_updated_at
    ).run();
  } catch (error) {
    console.error(`Error in storeDocument: ${error.message}`);
    throw error;
  }
}

// Helper functions (imported from projectService.js)
function isInCustomerFolder(item) {
  if (item.folder && item.folder.name) {
    return item.folder.name.toLowerCase().includes('customer');
  }
  return false;
}

function isInCustomerAlbum(photo) {
  if (photo.album && photo.album.name) {
    return photo.album.name.toLowerCase().includes('customer');
  }
  return false;
}

function sanitizeText(text) {
  if (!text) return '';
  
  let sanitized = text
    .replace(/\$\s*[\d,]+(\.\d{2})?/g, '[Cost Redacted]')
    .replace(/cost:?\s*[\d,]+(\.\d{2})?/gi, '[Cost Information Redacted]')
    .replace(/safety:?.*?(?=\n|$)/gi, '[Safety Information Redacted]')
    .replace(/crew:?.*?(?=\n|$)/gi, '[Crew Information Redacted]');
  
  return sanitized;
}

function getMilestoneTagFromUpdate(update) {
  const text = update.notes?.toLowerCase() || '';
  
  if (text.includes('order placed') || text.includes('order confirmed')) {
    return 'order_placed';
  } else if (text.includes('production start') || text.includes('manufacturing')) {
    return 'production_start';
  } else if (text.includes('shipped') || text.includes('shipping')) {
    return 'ship';
  } else if (text.includes('delivered') || text.includes('delivery')) {
    return 'delivery';
  } else if (text.includes('install') || text.includes('installation')) {
    return 'install';
  } else if (text.includes('punch') || text.includes('complete')) {
    return 'punch_complete';
  }
  
  return null;
}

function getFileType(filename) {
  if (!filename) return 'unknown';
  
  const extension = filename.split('.').pop().toLowerCase();
  
  const fileTypes = {
    'pdf': 'PDF',
    'doc': 'Word',
    'docx': 'Word',
    'xls': 'Excel',
    'xlsx': 'Excel',
    'ppt': 'PowerPoint',
    'pptx': 'PowerPoint',
    'jpg': 'Image',
    'jpeg': 'Image',
    'png': 'Image',
    'gif': 'Image',
    'txt': 'Text',
    'csv': 'CSV',
    'zip': 'Archive',
    'rar': 'Archive',
    'dwg': 'CAD',
    'dxf': 'CAD'
  };
  
  return fileTypes[extension] || 'Document';
}

function isDownloadAllowed(doc) {
  const sensitivePatterns = [
    'confidential',
    'internal',
    'private',
    'sensitive',
    'contract',
    'agreement',
    'financial'
  ];
  
  const title = (doc.title || doc.filename || '').toLowerCase();
  
  return !sensitivePatterns.some(pattern => title.includes(pattern));
}

// --- R2 Helpers for Photos ---
async function resolveCompanyId(projectId, env, token) {
  let companyId = 4276277;
  try {
    const list = await fetchProCoreAPI(`/rest/v1.1/projects?ids[]=${encodeURIComponent(projectId)}`, {}, env, token);
    if (Array.isArray(list)) {
      const match = list.find(p => p?.id?.toString() === String(projectId));
      if (match?.company?.id) companyId = match.company.id;
    }
  } catch (_) {}
  return companyId;
}

async function mirrorPhotoToR2(projectId, photoId, env) {
  const token = await getAccessToken(env);
  if (!token || !env.DOCS_BUCKET) return;
  const companyId = await resolveCompanyId(projectId, env, token);
  // Look up the photo row to get full_url and caption
  let row = await env.DB.prepare('SELECT full_url, caption FROM photos WHERE project_id = ? AND external_id = ?')
    .bind(String(projectId), String(photoId)).first();
  if (!row || !row.full_url) {
    // Refresh from Procore to get fresh signed URL
    try { await getProjectPhotos(projectId, env); } catch (_) {}
    row = await env.DB.prepare('SELECT full_url, caption FROM photos WHERE project_id = ? AND external_id = ?')
      .bind(String(projectId), String(photoId)).first();
  }
  const fullUrl = row?.full_url;
  if (!fullUrl) return;
  const headers = { 'Accept': '*/*', 'Authorization': `Bearer ${token}`, 'Procore-Company-Id': companyId.toString() };
  if (env.PROCORE_APP_VERSION_KEY) {
    headers['Procore-App-Version'] = env.PROCORE_APP_VERSION_KEY;
    headers['Procore-App-Version-Key'] = env.PROCORE_APP_VERSION_KEY;
  }
  let res = await fetch(fullUrl, { headers, redirect: 'follow' });
  if (!res.ok && (res.status === 401 || res.status === 403)) {
    try {
      await getProjectPhotos(projectId, env);
      const row2 = await env.DB.prepare('SELECT full_url FROM photos WHERE project_id = ? AND external_id = ?')
        .bind(String(projectId), String(photoId)).first();
      if (row2?.full_url) res = await fetch(row2.full_url, { headers, redirect: 'follow' });
    } catch (_) {}
  }
  if (!res.ok || !res.body) return;
  // Determine filename + extension and write to R2
  let contentType = (res.headers.get('Content-Type') || 'image/jpeg').toLowerCase();
  if (contentType === 'application/octet-stream' || contentType === 'binary/octet-stream') contentType = 'image/jpeg';
  const extMap = { 'image/jpeg':'jpg','image/jpg':'jpg','image/png':'png','image/webp':'webp','image/heic':'heic','image/heif':'heif','image/gif':'gif','image/bmp':'bmp' };
  const ext = extMap[contentType] || 'jpg';
  const safeBase = (row?.caption && row.caption.trim()) ? row.caption.trim().replace(/[^A-Za-z0-9._-]+/g, '_') : `photo-${photoId}`;
  const filename = safeBase.endsWith(`.${ext}`) ? safeBase : `${safeBase}.${ext}`;
  const key = `companies/${companyId}/projects/${projectId}/photos/${photoId}/${filename}`;
  const [, body] = res.body.tee();
  await env.DOCS_BUCKET.put(key, body, { httpMetadata: { contentType, contentDisposition: `inline; filename="${filename}"`, cacheControl: 'public, max-age=86400' }, customMetadata: { project_id: String(projectId), photo_id: String(photoId), company_id: String(companyId) } });
}

async function deletePhotoFromR2(projectId, photoId, env) {
  if (!env.DOCS_BUCKET) return;
  const token = await getAccessToken(env);
  const companyId = await resolveCompanyId(projectId, env, token);
  const prefixes = [
    `companies/${companyId}/projects/${projectId}/photos/${photoId}/`,
    `companies/${companyId}/projects/${projectId}/photos/${photoId}` // legacy
  ];
  for (const prefix of prefixes) {
    try {
      const listed = await env.DOCS_BUCKET.list({ prefix, limit: 1000 });
      for (const obj of (listed?.objects || [])) {
        try { await env.DOCS_BUCKET.delete(obj.key); } catch (_) {}
      }
    } catch (_) {}
  }
}

// --- R2 Helpers for Documents ---
async function mirrorDocumentToR2(projectId, docId, env) {
  const token = await getAccessToken(env);
  if (!token || !env.DOCS_BUCKET) return;
  const companyId = await resolveCompanyId(projectId, env, token);
  const newPrefix = `companies/${companyId}/projects/${projectId}/documents/${docId}/`;
  const legacyKey = `companies/${companyId}/projects/${projectId}/documents/${docId}`;

  // If a proper non-.bin object exists already, skip
  try {
    const listed = await env.DOCS_BUCKET.list({ prefix: newPrefix, limit: 10 });
    if (listed && listed.objects && listed.objects.length > 0) {
      const hasNonBin = listed.objects.some(o => o.key && !o.key.endsWith('.bin'));
      if (hasNonBin) return;
    }
  } catch (_) {}

  // If legacy object exists, migrate to new scheme using title and inferred type
  try {
    const obj = await env.DOCS_BUCKET.get(legacyKey);
    if (obj) {
      const row = await env.DB.prepare('SELECT title FROM documents WHERE project_id = ? AND external_id = ?')
        .bind(String(projectId), String(docId)).first();
      const title = (row?.title || `document-${docId}`).replace(/"/g, '');
      const extFromTitle = (title.match(/\.([A-Za-z0-9]{2,5})$/) || [,''])[1].toLowerCase();
      const map = { pdf:'application/pdf', jpg:'image/jpeg', jpeg:'image/jpeg', png:'image/png', webp:'image/webp', docx:'application/vnd.openxmlformats-officedocument.wordprocessingml.document', xlsx:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', pptx:'application/vnd.openxmlformats-officedocument.presentationml.presentation', doc:'application/msword', xls:'application/vnd.ms-excel', ppt:'application/vnd.ms-powerpoint', mp4:'video/mp4', mov:'video/quicktime', zip:'application/zip' };
      let ct = String(obj.httpMetadata?.contentType || '').toLowerCase();
      if (ct === '' || ct === 'application/octet-stream' || ct === 'binary/octet-stream') ct = map[extFromTitle] || 'application/octet-stream';
      const extByCT = { 'application/pdf':'pdf','image/jpeg':'jpg','image/jpg':'jpg','image/png':'png','image/webp':'webp','application/vnd.openxmlformats-officedocument.wordprocessingml.document':'docx','application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':'xlsx','application/vnd.openxmlformats-officedocument.presentationml.presentation':'pptx','application/msword':'doc','application/vnd.ms-excel':'xls','application/vnd.ms-powerpoint':'ppt','video/mp4':'mp4','video/quicktime':'mov','application/zip':'zip' };
      const ext = extByCT[ct] || (extFromTitle || 'bin');
      const baseName = title.replace(/[^A-Za-z0-9._-]+/g, '_');
      const filename = baseName.toLowerCase().endsWith(`.${ext.toLowerCase()}`) ? baseName : `${baseName}.${ext}`;
      const isInline = ct.startsWith('image/') || ct === 'application/pdf' || ct.startsWith('video/');
      const cdHeader = `${isInline ? 'inline' : 'attachment'}; filename="${filename}"`;
      const cache = ct === 'application/pdf' || ct.startsWith('image/') ? 'public, max-age=86400' : (ct.startsWith('video/') ? 'public, max-age=3600' : 'no-store');
      const newKey = `${newPrefix}${filename}`;
      await env.DOCS_BUCKET.put(newKey, obj.body, { httpMetadata: { contentType: ct, contentDisposition: cdHeader, cacheControl: cache }, customMetadata: { project_id: String(projectId), doc_id: String(docId), company_id: String(companyId) } });
      return;
    }
  } catch (_) {}

  // Else fetch a download URL via metadata and store
  const base = env.PROCORE_API_BASE_URL || 'https://api.procore.com';
  const headers = { 'Authorization': `Bearer ${token}`, 'Accept': '*/*', 'Procore-Company-Id': companyId.toString() };
  if (env.PROCORE_APP_VERSION_KEY) {
    headers['Procore-App-Version'] = env.PROCORE_APP_VERSION_KEY;
    headers['Procore-App-Version-Key'] = env.PROCORE_APP_VERSION_KEY;
  }
  const metaPaths = [
    `/rest/v1.0/files/${encodeURIComponent(docId)}?project_id=${encodeURIComponent(projectId)}`,
    `/rest/v1.0/documents/${encodeURIComponent(docId)}`,
    `/rest/v2.0/projects/${encodeURIComponent(projectId)}/documents/${encodeURIComponent(docId)}`,
    `/rest/v1.0/projects/${encodeURIComponent(projectId)}/documents/${encodeURIComponent(docId)}`
  ];
  let streamed = false;
  for (const m of metaPaths) {
    try {
      const meta = await fetchProCoreAPI(m, {}, env, token, companyId);
      const url = meta?.download_url || meta?.url || meta?.web_url || meta?.direct_download_url;
      if (!url) continue;
      const res = await fetch(url, { headers, redirect: 'follow' });
      if (!res.ok || !res.body) continue;
      const title = (meta?.title || meta?.name || meta?.filename || `document-${docId}`).replace(/"/g, '');
      let ct = (res.headers.get('Content-Type') || 'application/octet-stream').toLowerCase();
      const extFromTitle = (title.match(/\.([A-Za-z0-9]{2,5})$/) || [,''])[1].toLowerCase();
      if (ct === 'application/octet-stream' || ct === 'binary/octet-stream') {
        const map = { pdf:'application/pdf', jpg:'image/jpeg', jpeg:'image/jpeg', png:'image/png', webp:'image/webp', docx:'application/vnd.openxmlformats-officedocument.wordprocessingml.document', xlsx:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', pptx:'application/vnd.openxmlformats-officedocument.presentationml.presentation', doc:'application/msword', xls:'application/vnd.ms-excel', ppt:'application/vnd.ms-powerpoint', mp4:'video/mp4', mov:'video/quicktime', zip:'application/zip' };
        ct = map[extFromTitle] || 'application/octet-stream';
      }
      const extByCT = { 'application/pdf':'pdf','image/jpeg':'jpg','image/jpg':'jpg','image/png':'png','image/webp':'webp','application/vnd.openxmlformats-officedocument.wordprocessingml.document':'docx','application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':'xlsx','application/vnd.openxmlformats-officedocument.presentationml.presentation':'pptx','application/msword':'doc','application/vnd.ms-excel':'xls','application/vnd.ms-powerpoint':'ppt','video/mp4':'mp4','video/quicktime':'mov','application/zip':'zip' };
      const ext = extByCT[ct] || (extFromTitle || 'bin');
      const baseName = title.replace(/[^A-Za-z0-9._-]+/g, '_');
      const filename = baseName.toLowerCase().endsWith(`.${ext.toLowerCase()}`) ? baseName : `${baseName}.${ext}`;
      const isInline = ct.startsWith('image/') || ct === 'application/pdf' || ct.startsWith('video/');
      const cdHeader = `${isInline ? 'inline' : 'attachment'}; filename="${filename}"`;
      const cache = ct === 'application/pdf' || ct.startsWith('image/') ? 'public, max-age=86400' : (ct.startsWith('video/') ? 'public, max-age=3600' : 'no-store');
      const [, b] = res.body.tee();
      const newKey = `${newPrefix}${filename}`;
      await env.DOCS_BUCKET.put(newKey, b, { httpMetadata: { contentType: ct, contentDisposition: cdHeader, cacheControl: cache }, customMetadata: { project_id: String(projectId), doc_id: String(docId), company_id: String(companyId) } });
      streamed = true;
      break;
    } catch (_) {}
  }
  if (streamed) return;
}

async function deleteDocumentFromR2(projectId, docId, env) {
  if (!env.DOCS_BUCKET) return;
  const token = await getAccessToken(env);
  const companyId = await resolveCompanyId(projectId, env, token);
  const prefixes = [
    `companies/${companyId}/projects/${projectId}/documents/${docId}/`,
    `companies/${companyId}/projects/${projectId}/documents/${docId}`
  ];
  for (const prefix of prefixes) {
    try {
      const listed = await env.DOCS_BUCKET.list({ prefix, limit: 1000 });
      for (const obj of (listed?.objects || [])) {
        try { await env.DOCS_BUCKET.delete(obj.key); } catch (_) {}
      }
    } catch (_) {}
  }
}
